"""
Trains the meta-learner for the drug-disease repurposing ensemble model.

This script:
1. Loads the validation set predictions generated by train_base.py.
2. Creates enhanced meta-features including:
   - Basic classifier probabilities from 3 base classifiers
   - Statistical aggregations (mean, median, std, variance)
   - Interaction features (pairwise products)
   - Individual model confidence features (entropy-based)
3. Implements Multi-Level Stacking:
   - Level 2: Three diverse meta-learners:
     * Gaussian Process with RBF kernel on enhanced features
     * Neural: MLP on enhanced features  
     * Confidence: Weighted ensemble based on model confidence
   - Level 3: Final Logistic Regression combines base probabilities + Level 2 predictions
4. Evaluates the trained multi-level stacking model on validation data.
5. Saves the complete stacking model.

Usage:
    python train_meta.py [--val_pred_file_path PATH_TO_VAL_SET_PREDS.parquet]
"""

import os
import polars as pl # Changed from pandas to polars
import numpy as np
import argparse
import logging
import pickle
from joblib import dump
from scipy.stats import entropy

from sklearn.linear_model import LogisticRegression
import lightgbm as lgb
from sklearn.gaussian_process import GaussianProcessClassifier
from sklearn.gaussian_process.kernels import RBF
from sklearn.neural_network import MLPClassifier

from inference import EnsemblePredictor

from train_base import load_and_split_prepared_dataset

from config import (
    DATA_DIR, MODELS_DIR, LOGS_DIR, SEED, DEFAULT_FEATURE_COL
)
from utils import (
    setup_logger, get_timestamp, evaluate_predictions, prepare_drug_disease_evaluation_data,
    create_enhanced_meta_features, improved_confidence_weighting
)

# Setup logging
timestamp = get_timestamp()
os.makedirs(LOGS_DIR, exist_ok=True)
os.makedirs(MODELS_DIR, exist_ok=True)
log_file = os.path.join(LOGS_DIR, f"train_meta_{timestamp}.log")
logger = setup_logger("train_meta", log_file)

np.random.seed(SEED)

def load_validation_set_predictions(val_pred_file_path=None):
    """
    Load validation set predictions from train_base.py.
    If no path is provided, attempts to load the default file from the updated directory.
    """
    if val_pred_file_path is None:
        # Updated default path for validation set predictions
        val_pred_file_path = os.path.join(DATA_DIR, "validation_set_predictions", "val_set_base_predictions.parquet")

    logger.info(f"Loading validation set base model predictions from: {val_pred_file_path}")
    try:
        val_pred_df = pl.read_parquet(val_pred_file_path)
        logger.info(f"Loaded Polars validation set predictions with shape: {val_pred_df.shape}")
        
        required_cols = [
            'labels', 'drug_ids', 'dis_ids',
            'xgb_clf_proba', 'lgbm_clf_proba', 'catboost_clf_proba'
        ]
        missing_cols = [col for col in required_cols if col not in val_pred_df.columns]
        if missing_cols:
            logger.error(f"Missing required columns in validation set predictions: {missing_cols}")
            raise ValueError(f"Validation set predictions file is missing columns: {missing_cols}")

        return val_pred_df
    except FileNotFoundError:
        logger.error(f"Validation set predictions file not found: {val_pred_file_path}. Run train_base.py first.")
        raise
    except Exception as e:
        logger.error(f"Error loading validation set predictions: {e}", exc_info=True)
        raise

def prepare_meta_learning_data(val_pred_df: pl.DataFrame):
    """
    Prepare and clean data for multi-level stacking.
    """
    logger.info("Preparing data for multi-level stacking...")

    if val_pred_df.is_empty():
        logger.warning("Input DataFrame for meta-learning is empty.")
        return None

    # Check required columns
    required_cols = [
        'labels', 'drug_ids', 'dis_ids',
        'xgb_clf_proba', 'lgbm_clf_proba', 'catboost_clf_proba', 'tabnet_clf_proba'
    ]
    missing_cols = [col for col in required_cols if col not in val_pred_df.columns]
    if missing_cols:
        logger.error(f"Missing required columns: {missing_cols}")
        return None

    # Rename columns to match what prepare_drug_disease_evaluation_data expects
    if 'drug_ids' in val_pred_df.columns:
        val_pred_df = val_pred_df.rename({'drug_ids': 'drug_id'})
    if 'dis_ids' in val_pred_df.columns:
        val_pred_df = val_pred_df.rename({'dis_ids': 'dis_id'})

    # Filter out rows with NaN values in essential columns
    essential_cols = ['labels', 'xgb_clf_proba', 'lgbm_clf_proba', 'catboost_clf_proba', 'tabnet_clf_proba']
    filter_conditions = [pl.col(c).is_not_null() for c in essential_cols]
    cleaned_val_pred_df = val_pred_df.filter(pl.all_horizontal(filter_conditions))

    if cleaned_val_pred_df.height < val_pred_df.height:
        logger.warning(f"Dropped {val_pred_df.height - cleaned_val_pred_df.height} rows with NaN values")

    if cleaned_val_pred_df.is_empty():
        logger.error("No valid data available after cleaning")
        return None

    logger.info(f"Prepared {cleaned_val_pred_df.height} samples for meta-learning")
    return cleaned_val_pred_df

def train_multi_level_stacking(val_pred_df: pl.DataFrame):
    """
    Train a multi-level stacking ensemble with diverse meta-learners.
    
    Architecture:
    Level 2: Three diverse meta-learners:
        1. Gaussian Process with RBF kernel on enhanced features 
        2. Neural: MLP on enhanced features
        3. Confidence: Weighted ensemble based on model confidence
    Level 3: Final Logistic Regression combines:
        - 3 base model probabilities (direct access)
        - 3 Level 2 meta-predictions
        Total: 6 features for optimal combination
    """
    logger.info("Training Multi-Level Stacking ensemble...")
    
    y_labels = val_pred_df['labels'].to_numpy().astype(int)
    
    # Level 2: Train diverse meta-learners
    logger.info("Level 2: Training diverse meta-learners...")
    meta_learners = {}
    meta_predictions = {}
    
    # Get base model probabilities to pass directly to Level 3
    basic_features = ['xgb_clf_proba', 'lgbm_clf_proba', 'catboost_clf_proba', 'tabnet_clf_proba']
    base_probabilities = val_pred_df.select(basic_features).to_numpy()
    logger.info("Base model probabilities will be passed directly to Level 3")
    
    # 1. Nonlinear meta-learner with Gaussian Process (enhanced features)
    logger.info("Training Gaussian Process meta-learner with enhanced features...")
    enhanced_df, enhanced_features = create_enhanced_meta_features(val_pred_df)
    X_enhanced = enhanced_df.select(enhanced_features).to_numpy()
    
    # Use RBF kernel with appropriate length scale
    kernel = RBF(length_scale=1.0, length_scale_bounds=(1e-2, 1e2))
    meta_learners['gaussian_process'] = GaussianProcessClassifier(
        kernel=kernel,
        random_state=SEED,
        max_iter_predict=100,
        warm_start=True
    )
    meta_learners['gaussian_process'].fit(X_enhanced, y_labels)
    meta_predictions['gaussian_process'] = meta_learners['gaussian_process'].predict_proba(X_enhanced)[:, 1]
    logger.info(f"Gaussian Process meta-learner trained. Score range: [{meta_predictions['gaussian_process'].min():.3f}, {meta_predictions['gaussian_process'].max():.3f}]")
    
    # 2. Neural Network meta-learner (MLP)
    logger.info("Training MLP meta-learner...")
    meta_learners['neural'] = MLPClassifier(
        hidden_layer_sizes=(32, 16),
        activation='relu',
        solver='adam',
        alpha=0.001,
        max_iter=500,
        random_state=SEED,
        early_stopping=True,
        validation_fraction=0.2
    )
    meta_learners['neural'].fit(X_enhanced, y_labels)
    meta_predictions['neural'] = meta_learners['neural'].predict_proba(X_enhanced)[:, 1]
    logger.info(f"MLP meta-learner trained. Score range: [{meta_predictions['neural'].min():.3f}, {meta_predictions['neural'].max():.3f}]")
    
    # 3. Confidence-based meta-learner
    logger.info("Computing confidence-based predictions...")
    conf_df = improved_confidence_weighting(val_pred_df)
    meta_predictions['confidence'] = conf_df['confidence_weighted_avg'].to_numpy()
    logger.info(f"Confidence-based predictions computed. Score range: [{meta_predictions['confidence'].min():.3f}, {meta_predictions['confidence'].max():.3f}]")
    
    # Level 3: Final meta-learner combines base probabilities + Level 2 predictions
    logger.info("Level 3: Training final meta-learner...")
    X_final = np.column_stack([
        base_probabilities,  # 3 base model probabilities
        meta_predictions['gaussian_process'],  # 1 GP prediction
        meta_predictions['neural'],  # 1 MLP prediction
        meta_predictions['confidence']  # 1 confidence prediction
    ])
    logger.info(f"Final meta-learner input shape: {X_final.shape} (4 base + 3 meta predictions)")
    
    final_meta_learner = LogisticRegression(
        C=0.1,  # Regularization to avoid overfitting
        random_state=SEED,
        solver='liblinear'
    )
    final_meta_learner.fit(X_final, y_labels)
    
    logger.info("Multi-Level Stacking training completed!")
    logger.info(f"Final meta-learner coefficients: {final_meta_learner.coef_[0]}")
    logger.info(f"Final meta-learner intercept: {final_meta_learner.intercept_[0]}")
    
    # Create a comprehensive model container
    stacking_model = {
        'level2_models': meta_learners,
        'final_model': final_meta_learner,
        'basic_features': basic_features
    }
    
    return stacking_model

def evaluate_stacking_model(stacking_model, val_df_pl):
    """Evaluate the multi-level stacking model."""
    logger.info("Evaluating Multi-Level Stacking model on validation set...")
    
    # Prepare drug-disease evaluation data (with polars-u64-idx support)
    logger.info("Preparing drug-disease evaluation data...")
    val_drug_disease_data = prepare_drug_disease_evaluation_data(val_df_pl)
    logger.info(f"Created evaluation matrix with {val_drug_disease_data.shape[0]:,} drug-disease pairs")

    # Initialize the ensemble predictor
    logger.info("Initializing ensemble predictor with trained stacking model...")
    final_predictor = EnsemblePredictor(stacking_model=stacking_model)
    
    # Get feature vectors from the validation data
    logger.info("Extracting feature vectors...")
    X_features_np = np.array(val_drug_disease_data[DEFAULT_FEATURE_COL].to_list())
    logger.info(f"Making predictions on {X_features_np.shape[0]:,} validation drug-disease pairs...")
    
    # Get ensemble predictions
    y_pred_proba = final_predictor.predict_ensemble_for_features(X_features_np)
    logger.info("Predictions completed. Adding to evaluation data...")
    
    # Add predictions to the validation data
    val_drug_disease_data = val_drug_disease_data.with_columns(
        pl.lit(y_pred_proba).alias("treat score")
    )
    
    # Evaluate
    logger.info("Computing evaluation metrics...")
    eval_metrics = evaluate_predictions(val_drug_disease_data)
    
    logger.info("Multi-Level Stacking validation set evaluation metrics:")
    logger.info("VALID Classification Metrics: ")
    logger.info(f"Accuracy: {eval_metrics['accuracy']:.4f}")
    logger.info(f"Precision: {eval_metrics['precision']:.4f}")
    logger.info(f"Recall: {eval_metrics['recall']:.4f}")
    logger.info(f"F1: {eval_metrics['f1']:.4f}")
    logger.info(f"PR-AUC: {eval_metrics['pr_auc']:.4f}")
    logger.info(f"LogLoss: {eval_metrics['logloss']:.4f}")
    
    logger.info("VALID Ranking Metrics: ")
    logger.info(f"MRR: {eval_metrics['mrr']:.4f}")
    logger.info(f"Hit@1: {eval_metrics['hit@1']:.4f}")
    logger.info(f"Hit@5: {eval_metrics['hit@5']:.4f}")
    logger.info(f"Hit@10: {eval_metrics['hit@10']:.4f}")
    logger.info(f"Hit@20: {eval_metrics['hit@20']:.4f}")
    logger.info(f"Hit@100: {eval_metrics['hit@100']:.4f}")
    logger.info(f"Recall@20000: {eval_metrics['recall@20000']:.4f}")
    logger.info(f"Recall@40000: {eval_metrics['recall@40000']:.4f}")
    logger.info(f"Recall@60000: {eval_metrics['recall@60000']:.4f}")
    logger.info(f"Recall@80000: {eval_metrics['recall@80000']:.4f}")
    logger.info(f"Recall@100000: {eval_metrics['recall@100000']:.4f}")

def main(val_pred_file_path=None):
    """
    Main function to run the multi-level stacking training pipeline.
    """
    logger.info("Starting Multi-Level Stacking meta-learner training pipeline...")
    
    # Load validation predictions
    val_pred_df = load_validation_set_predictions(val_pred_file_path)
    
    # Check if data is usable
    if val_pred_df.is_empty() or ('labels' in val_pred_df.columns and val_pred_df['labels'].is_null().all()):
        logger.error("Loaded validation set predictions DataFrame is empty or contains no valid labels. Aborting training.")
        return

    # Prepare data for multi-level stacking
    cleaned_val_pred_df = prepare_meta_learning_data(val_pred_df)
    
    if cleaned_val_pred_df is None:
        logger.error("Data preparation failed. Aborting training.")
        return

    # Train multi-level stacking model
    stacking_model = train_multi_level_stacking(cleaned_val_pred_df)
    
    if stacking_model is None:
        logger.error("Multi-level stacking training failed. Model will not be saved.")
        return

    # Save the stacking model
    stacking_model_filename = "multi_level_stacking_model.joblib"
    stacking_model_save_path = os.path.join(MODELS_DIR, stacking_model_filename)
    dump(stacking_model, stacking_model_save_path)
    logger.info(f"Multi-Level Stacking model saved to: {stacking_model_save_path}")
    
    # # Evaluate the model
    # _, val_df_pl, _ = load_and_split_prepared_dataset()
    # evaluate_stacking_model(stacking_model, val_df_pl)
    
    # logger.info("Multi-Level Stacking training pipeline completed successfully!")
    
    # # Evaluate the entire model on the test set
    # logger.info("Starting test set evaluation...")
    # _, _, test_df_pl = load_and_split_prepared_dataset()
    
    # logger.info(f"Preparing test drug-disease evaluation data for {test_df_pl.shape[0]} test samples...")
    # test_drug_disease_data = prepare_drug_disease_evaluation_data(test_df_pl)
    
    # # Initialize the ensemble predictor
    # final_predictor = EnsemblePredictor()
    
    # # Get feature vectors from the test data
    # X_features_np = np.array(test_drug_disease_data[DEFAULT_FEATURE_COL].to_list())
    # logger.info(f"Predicting on {X_features_np.shape[0]} test drug-disease pairs...")
    
    # # Get ensemble predictions
    # y_pred_proba = final_predictor.predict_ensemble_for_features(X_features_np)
    
    # # Add predictions to the test data
    # test_drug_disease_data = test_drug_disease_data.with_columns(
    #     pl.lit(y_pred_proba).alias("treat score")
    # )
    
    # # Evaluate predictions
    # eval_metrics = evaluate_predictions(test_drug_disease_data)
    # logger.info("Meta-learner test set evaluation metrics:")
    
    # logger.info("TEST Classification Metrics: ")
    # logger.info(f"Accuracy: {eval_metrics['accuracy']:.4f}")
    # logger.info(f"Precision: {eval_metrics['precision']:.4f}")
    # logger.info(f"Recall: {eval_metrics['recall']:.4f}")
    # logger.info(f"F1: {eval_metrics['f1']:.4f}")
    # logger.info(f"PR-AUC: {eval_metrics['pr_auc']:.4f}")
    # logger.info(f"LogLoss: {eval_metrics['logloss']:.4f}")
    
    # logger.info("TEST Ranking Metrics: ")
    # logger.info(f"MRR: {eval_metrics['mrr']:.4f}")
    # logger.info(f"Hit@1: {eval_metrics['hit@1']:.4f}")
    # logger.info(f"Hit@5: {eval_metrics['hit@5']:.4f}")
    # logger.info(f"Hit@10: {eval_metrics['hit@10']:.4f}")
    # logger.info(f"Hit@20: {eval_metrics['hit@20']:.4f}")
    # logger.info(f"Hit@100: {eval_metrics['hit@100']:.4f}")
    # logger.info(f"Recall@20000: {eval_metrics['recall@20000']:.4f}")
    # logger.info(f"Recall@40000: {eval_metrics['recall@40000']:.4f}")
    # logger.info(f"Recall@60000: {eval_metrics['recall@60000']:.4f}")
    # logger.info(f"Recall@80000: {eval_metrics['recall@80000']:.4f}")
    # logger.info(f"Recall@100000: {eval_metrics['recall@100000']:.4f}")
    # logger.info("Meta-learner test set evaluation completed successfully.")
            

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Train Multi-Level Stacking Meta-learner")
    parser.add_argument("--val_pred_file_path", type=str, default=None, 
                        help="Path to validation set predictions parquet file")
    
    args = parser.parse_args()
    main(val_pred_file_path=args.val_pred_file_path)
