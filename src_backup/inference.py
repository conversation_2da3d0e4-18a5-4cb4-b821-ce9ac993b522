# inference.py
"""
Inference script for the drug-disease repurposing ensemble model.

This script provides functions to:
1. Load all trained base models and the multi-level stacking model.
2. For a given drug-disease pair (or a batch of pairs):
   a. Construct the feature vector from their embeddings (enhanced or basic).
   b. Get predictions from all 3 base classifiers.
   c. Create enhanced meta-features and run through Level 2 meta-learners.
   d. Combine base probabilities + Level 2 predictions for final prediction.

"""

import os
# import pandas as pd # Removed pandas import
import numpy as np
import argparse
import logging
import pickle
import polars as pl
from joblib import load as joblib_load

from config import (
    DATA_DIR, MODELS_DIR, LOGS_DIR, SEED
)
from utils import (
    setup_logger, get_timestamp, combine_embeddings,
    create_enhanced_embeddings_batch_optimized
)

# Import enhanced meta-features functions from train_meta
from utils import create_enhanced_meta_features, improved_confidence_weighting

# Setup logging
timestamp = get_timestamp()
os.makedirs(LOGS_DIR, exist_ok=True) # Ensure logs dir exists
log_file = os.path.join(LOGS_DIR, f"inference_{timestamp}.log")
logger = setup_logger("inference", log_file)

np.random.seed(SEED)

# Define base model names for Level 1 predictions
# These are the 4 base classifiers in our ensemble
BASE_MODEL_NAMES = [
    "xgb_clf",
    "lgbm_clf", 
    "catboost_clf",
    "tabnet_clf"
]

CLASSIFIER_PROBA_SUFFIX = "_proba"

class EnsemblePredictor:
    """
    A complete ensemble predictor that loads all base models and the stacking model,
    then can predict on new drug-disease pairs using enhanced or basic embeddings.
    """

    def __init__(self, models_dir=MODELS_DIR, stacking_model=None, use_enhanced_embeddings=True, k=5, exp_lambda=0.7):
        """
        Initialize the ensemble predictor.

        Args:
            models_dir (str): Directory containing the trained base models and stacking model.
            stacking_model: Pre-loaded stacking model (optional).
            use_enhanced_embeddings (bool): Whether to use enhanced embeddings when possible.
            k (int): Number of top similar nodes for enhanced embeddings.
            exp_lambda (float): Lambda parameter for exponential rarity weighting.
        """
        self.models_dir = models_dir
        self.base_models_dir = os.path.join(models_dir, "base_models")
        self.use_enhanced_embeddings = use_enhanced_embeddings
        self.k = k
        self.exp_lambda = exp_lambda
        
        # Model storage
        self.base_models = {}
        self.base_models_loaded_status = False
        self.stacking_model = stacking_model
        self.stacking_model_loaded = stacking_model is not None
        
        # Enhanced embeddings data (loaded on-demand)
        self.enhanced_data_loaded = False
        self.bi_kg_edge = None
        self.idx_map = None
        
        # Precomputed enhanced embedding data (for batch efficiency)
        self.precomputed_data = None
        self.precomputed_data_loaded = False
        
        logger.info(f"EnsemblePredictor initialized (enhanced_embeddings={use_enhanced_embeddings}, k={k}, lambda={exp_lambda})")

    def load_base_models(self):
        """
        Load all base models (XGB, LGBM, CatBoost, TabNet).
        """
        logger.info("Loading base models...")
        
        expected_filenames = {
            "xgb_clf": "xgb_clf.joblib",
            "lgbm_clf": "lgbm_clf.joblib",
            "catboost_clf": "catboost_clf.joblib",
            "tabnet_clf": "tabnet_clf.joblib"
        }

        for model_name in BASE_MODEL_NAMES:
            model_file = expected_filenames[model_name]
            model_path = os.path.join(self.base_models_dir, model_file)
            
            if not os.path.exists(model_path):
                raise FileNotFoundError(f"Base model not found: {model_path}")
            
            try:
                self.base_models[model_name] = joblib_load(model_path)
                logger.info(f"Loaded {model_name} from {model_path}")
            except Exception as e:
                logger.error(f"Failed to load {model_name} from {model_path}: {e}")
                raise

        self.base_models_loaded_status = True
        logger.info("All base models loaded successfully.")

    def load_stacking_model(self, stacking_model_path=None):
        """
        Load the multi-level stacking model.

        Args:
            stacking_model_path (str): Path to the stacking model. If None, uses default path.
        """
        if self.stacking_model_loaded:
            logger.info("Stacking model already loaded.")
            return

        if stacking_model_path is None:
            stacking_model_path = os.path.join(self.models_dir, "multi_level_stacking_model.joblib")

        if not os.path.exists(stacking_model_path):
            raise FileNotFoundError(f"Stacking model not found: {stacking_model_path}")

        try:
            self.stacking_model = joblib_load(stacking_model_path)
            self.stacking_model_loaded = True
            logger.info(f"Loaded stacking model from {stacking_model_path}")
        except Exception as e:
            logger.error(f"Failed to load stacking model from {stacking_model_path}: {e}")
            raise

    def load_enhanced_embedding_data(self):
        """Load data required for enhanced embeddings (bi_kg_edge and idx_map)."""
        if self.enhanced_data_loaded:
            return
            
        logger.info("Loading enhanced embedding data...")
        
        # Load bidirectional knowledge graph edges
        bi_kg_edge_path = os.path.join(DATA_DIR, "cached", "bi_kg_edge.parquet")
        if not os.path.exists(bi_kg_edge_path):
            logger.warning(f"bi_kg_edge.parquet not found at {bi_kg_edge_path}. Enhanced embeddings unavailable.")
            self.use_enhanced_embeddings = False
            return
        self.bi_kg_edge = pl.read_parquet(bi_kg_edge_path)
        
        # Load idx_map
        idx_map_path = os.path.join(DATA_DIR, "cached", "idx_map.pkl")
        if not os.path.exists(idx_map_path):
            logger.warning(f"idx_map.pkl not found at {idx_map_path}. Enhanced embeddings unavailable.")
            self.use_enhanced_embeddings = False
            return
        with open(idx_map_path, "rb") as f:
            self.idx_map = pickle.load(f)
        
        self.enhanced_data_loaded = True
        logger.info("Enhanced embedding data loaded successfully.")

    def precompute_enhanced_embedding_data(self, node_embedding_dict):
        """
        Precompute all expensive operations needed for enhanced embeddings.
        This should be called once before processing multiple batches.
        
        Args:
            node_embedding_dict (dict): Dictionary mapping node IDs to embeddings
        """
        if self.precomputed_data_loaded:
            logger.info("Enhanced embedding data already precomputed.")
            return
            
        if not self.use_enhanced_embeddings:
            logger.info("Enhanced embeddings disabled, skipping precomputation.")
            return
            
        # Ensure enhanced embedding data is loaded
        if not self.enhanced_data_loaded:
            self.load_enhanced_embedding_data()
            
        if not self.enhanced_data_loaded:
            logger.warning("Enhanced embedding data not available, skipping precomputation.")
            return
            
        logger.info("Precomputing expensive operations for enhanced embeddings...")
        logger.info("This will take several minutes but only needs to be done once...")
        
        # Import the functions from utils
        from utils import precompute_profiles_from_edges, precompute_similarity_rows_to_disk
        
        # Step 1: Pre-compute all node profiles (with caching) - This is the expensive part!
        logger.info("Step 1/2: Precomputing node profiles (this takes ~15+ minutes)...")
        (drug_profiles_dict, disease_profiles_dict, 
         drug_embeddings_dict, disease_embeddings_dict,
         drug_degrees_dict, disease_degrees_dict) = precompute_profiles_from_edges(
            self.bi_kg_edge, self.idx_map, node_embedding_dict
        )
        
        # Step 2: Pre-compute similarity rows to disk (memory-efficient)
        logger.info("Step 2/2: Precomputing similarity row mappings...")
        (drug_id_to_idx, disease_id_to_idx) = precompute_similarity_rows_to_disk(
            drug_profiles_dict, disease_profiles_dict
        )
        
        # Create reverse mappings for efficiency
        drug_idx_to_id = {idx: drug_id for drug_id, idx in drug_id_to_idx.items()}
        disease_idx_to_id = {idx: disease_id for disease_id, idx in disease_id_to_idx.items()}
        
        # Store precomputed data
        self.precomputed_data = {
            'drug_embeddings_dict': drug_embeddings_dict,
            'disease_embeddings_dict': disease_embeddings_dict,
            'drug_degrees_dict': drug_degrees_dict,
            'disease_degrees_dict': disease_degrees_dict,
            'drug_id_to_idx': drug_id_to_idx,
            'disease_id_to_idx': disease_id_to_idx,
            'drug_idx_to_id': drug_idx_to_id,
            'disease_idx_to_id': disease_idx_to_id
        }
        
        self.precomputed_data_loaded = True
        logger.info("Precomputation completed successfully!")
        logger.info(f"Precomputed data for {len(drug_embeddings_dict)} drugs and {len(disease_embeddings_dict)} diseases")

    def create_features_for_pairs_optimized(self, pairs_data, node_embedding_dict):
        """
        Create feature vectors for drug-disease pairs using precomputed data when available.
        This is much faster than the original method for batch processing.
        
        Args:
            pairs_data: List of (drug_id, disease_id) tuples or DataFrame with drug_id/dis_id columns
            node_embedding_dict: Dictionary mapping node IDs to embeddings
            
        Returns:
            tuple: (feature_vectors, valid_indices) where feature_vectors is list of feature arrays
                   and valid_indices indicates which input pairs were successfully processed
        """
        # Convert to DataFrame format for consistency
        if isinstance(pairs_data, list):
            pairs_df = pl.DataFrame({
                'drug_id': [pair[0] for pair in pairs_data],
                'dis_id': [pair[1] for pair in pairs_data]
            })
        else:
            # Assume it's already a DataFrame
            pairs_df = pairs_data
            
        # Try enhanced embeddings with precomputed data first
        if self.use_enhanced_embeddings and self.precomputed_data_loaded:
            try:
                logger.info(f"Creating enhanced embeddings using precomputed data for {pairs_df.shape[0]} pairs...")
                
                # Import the optimized function
                from utils import get_enhanced_embeddings_cached, combine_embeddings
                
                # Extract precomputed data
                drug_embeddings_dict = self.precomputed_data['drug_embeddings_dict']
                disease_embeddings_dict = self.precomputed_data['disease_embeddings_dict']
                drug_degrees_dict = self.precomputed_data['drug_degrees_dict']
                disease_degrees_dict = self.precomputed_data['disease_degrees_dict']
                drug_id_to_idx = self.precomputed_data['drug_id_to_idx']
                disease_id_to_idx = self.precomputed_data['disease_id_to_idx']
                drug_idx_to_id = self.precomputed_data['drug_idx_to_id']
                disease_idx_to_id = self.precomputed_data['disease_idx_to_id']
                
                # Find unique drugs and diseases to optimize computation
                unique_drugs = set(pairs_df['drug_id'].unique().to_list())
                unique_diseases = set(pairs_df['dis_id'].unique().to_list())
                
                logger.info(f"Found {len(unique_drugs)} unique drugs and {len(unique_diseases)} unique diseases")
                
                # Pre-compute enhanced embeddings for unique nodes only
                enhanced_drug_cache = {}
                for drug_id in unique_drugs:
                    if drug_id in drug_embeddings_dict and drug_id in drug_id_to_idx:
                        enhanced_emb = get_enhanced_embeddings_cached(
                            drug_id, 'drug', drug_embeddings_dict, drug_degrees_dict, 
                            drug_id_to_idx, drug_idx_to_id, self.k, self.exp_lambda
                        )
                        if enhanced_emb is not None:
                            enhanced_drug_cache[drug_id] = enhanced_emb
                
                enhanced_disease_cache = {}
                for disease_id in unique_diseases:
                    if disease_id in disease_embeddings_dict and disease_id in disease_id_to_idx:
                        enhanced_emb = get_enhanced_embeddings_cached(
                            disease_id, 'disease', disease_embeddings_dict, disease_degrees_dict,
                            disease_id_to_idx, disease_idx_to_id, self.k, self.exp_lambda
                        )
                        if enhanced_emb is not None:
                            enhanced_disease_cache[disease_id] = enhanced_emb
                
                # Fast O(1) lookup for each pair
                feature_vectors = []
                valid_indices = []
                
                for idx, row in enumerate(pairs_df.iter_rows(named=True)):
                    drug_id = row['drug_id']
                    dis_id = row['dis_id']
                    
                    # O(1) lookup instead of O(K) computation
                    enhanced_drug_emb = enhanced_drug_cache.get(drug_id)
                    enhanced_dis_emb = enhanced_disease_cache.get(dis_id)
                    
                    if enhanced_drug_emb is not None and enhanced_dis_emb is not None:
                        # Combine embeddings
                        combined_emb = combine_embeddings(enhanced_drug_emb, enhanced_dis_emb)
                        feature_vectors.append(combined_emb)
                        valid_indices.append(idx)
                
                if len(valid_indices) > 0:
                    logger.info(f"Successfully created enhanced embeddings for {len(valid_indices)} pairs using precomputed data")
                    return feature_vectors, valid_indices
                else:
                    logger.warning("No enhanced embeddings could be created, falling back to basic embeddings")
                    
            except Exception as e:
                logger.warning(f"Enhanced embeddings with precomputed data failed: {e}. Falling back to basic embeddings.")
        
        # Fall back to the original method (basic embeddings or non-optimized enhanced embeddings)
        return self.create_features_for_pairs(pairs_data, node_embedding_dict)

    def predict_ensemble_for_features(self, X_features_np):
        """
        Given feature vectors, run them through the ensemble pipeline.

        Args:
            X_features_np (np.ndarray): Feature matrix of shape (n_samples, n_features).

        Returns:
            np.ndarray: Final ensemble predictions (probabilities).
        """
        if not self.base_models_loaded_status:
            self.load_base_models()
        if not self.stacking_model_loaded:
            self.load_stacking_model()

        if X_features_np.ndim == 1:
            X_features_np = X_features_np.reshape(1, -1)
        num_samples = X_features_np.shape[0]

        # Step 1: Get base model predictions
        base_outputs = {}
        for model_name in BASE_MODEL_NAMES:
            y_pred_proba = self.base_models[model_name].predict_proba(X_features_np)[:, 1]
            base_outputs[model_name + CLASSIFIER_PROBA_SUFFIX] = y_pred_proba

        # Create a temporary Polars DataFrame for meta-feature creation
        classifier_prob_keys = [f"{name}{CLASSIFIER_PROBA_SUFFIX}" for name in BASE_MODEL_NAMES]
        base_probs_dict = {key: base_outputs[key] for key in classifier_prob_keys}
        # Add dummy columns that the functions expect
        base_probs_dict['labels'] = np.zeros(num_samples)  # Dummy labels
        base_probs_dict['drug_ids'] = [f"drug_{i}" for i in range(num_samples)]  # Dummy IDs
        base_probs_dict['dis_ids'] = [f"disease_{i}" for i in range(num_samples)]  # Dummy IDs
        
        val_pred_df = pl.DataFrame(base_probs_dict)
        
        # Step 2: Create enhanced meta-features for Level 2 models
        enhanced_df, enhanced_features = create_enhanced_meta_features(val_pred_df)
        X_enhanced = enhanced_df.select(enhanced_features).to_numpy()
        
        # Step 3: Get Level 2 predictions
        level2_predictions = {}
        
        # Gaussian Process prediction
        gp_pred = self.stacking_model['level2_models']['gaussian_process'].predict_proba(X_enhanced)[:, 1]
        level2_predictions['gaussian_process'] = gp_pred
        
        # Neural Network prediction
        neural_pred = self.stacking_model['level2_models']['neural'].predict_proba(X_enhanced)[:, 1]
        level2_predictions['neural'] = neural_pred
        
        # Confidence-based prediction
        conf_df = improved_confidence_weighting(val_pred_df)
        level2_predictions['confidence'] = conf_df['confidence_weighted_avg'].to_numpy()
        
        # Step 4: Combine base probabilities + Level 2 predictions for final prediction
        base_probabilities = np.column_stack([base_outputs[key] for key in classifier_prob_keys])
        X_final = np.column_stack([
            base_probabilities,  # 4 base model probabilities
            level2_predictions['gaussian_process'],  # 1 GP prediction
            level2_predictions['neural'],  # 1 MLP prediction  
            level2_predictions['confidence']  # 1 confidence prediction
        ])
        
        # Step 5: Final prediction using Level 3 model
        final_probabilities = self.stacking_model['final_model'].predict_proba(X_final)[:, 1]
        return final_probabilities

    def create_features_for_pairs(self, pairs_data, node_embedding_dict):
        """
        Create feature vectors for drug-disease pairs using enhanced or basic embeddings.
        
        Args:
            pairs_data: List of (drug_id, disease_id) tuples or DataFrame with drug_id/dis_id columns
            node_embedding_dict: Dictionary mapping node IDs to embeddings
            
        Returns:
            tuple: (feature_vectors, valid_indices) where feature_vectors is list of feature arrays
                   and valid_indices indicates which input pairs were successfully processed
        """
        # Convert to DataFrame format for consistency
        if isinstance(pairs_data, list):
            pairs_df = pl.DataFrame({
                'drug_id': [pair[0] for pair in pairs_data],
                'dis_id': [pair[1] for pair in pairs_data]
            })
        else:
            # Assume it's already a DataFrame
            pairs_df = pairs_data
            
        feature_vectors = []
        valid_indices = []
        
        # Try enhanced embeddings first if enabled and data is available
        if self.use_enhanced_embeddings:
            try:
                if not self.enhanced_data_loaded:
                    self.load_enhanced_embedding_data()
                
                if self.enhanced_data_loaded:
                    logger.info(f"Creating enhanced embeddings for {pairs_df.shape[0]} pairs...")
                    enhanced_embeddings, enhanced_valid_indices = create_enhanced_embeddings_batch_optimized(
                        pairs_df, self.bi_kg_edge, self.idx_map, node_embedding_dict, 
                        k=self.k, exp_lambda=self.exp_lambda
                    )
                    
                    if len(enhanced_valid_indices) > 0:
                        logger.info(f"Successfully created enhanced embeddings for {len(enhanced_valid_indices)} pairs")
                        return enhanced_embeddings, enhanced_valid_indices
                    else:
                        logger.warning("No enhanced embeddings could be created, falling back to basic embeddings")
                        
            except Exception as e:
                logger.warning(f"Enhanced embeddings failed: {e}. Falling back to basic embeddings.")
        
        # Fall back to basic embeddings
        logger.info(f"Creating basic embeddings for {pairs_df.shape[0]} pairs...")
        for idx, row in enumerate(pairs_df.iter_rows(named=True)):
            drug_id = row['drug_id']
            dis_id = row['dis_id']
            
            drug_emb = node_embedding_dict.get(drug_id)
            dis_emb = node_embedding_dict.get(dis_id)
            
            if drug_emb is not None and dis_emb is not None:
                try:
                    combined_emb = combine_embeddings(drug_emb, dis_emb)
                    feature_vectors.append(combined_emb)
                    valid_indices.append(idx)
                except Exception as e:
                    logger.warning(f"Error combining embeddings for {drug_id}-{dis_id}: {e}")
            else:
                logger.warning(f"Missing embedding for {drug_id} or {dis_id}")
        
        logger.info(f"Created basic embeddings for {len(valid_indices)} out of {pairs_df.shape[0]} pairs")
        return feature_vectors, valid_indices

    def predict_single_pair(self, drug_id: str, disease_id: str, node_embedding_dict: dict):
        """
        Predicts the indication probability for a single drug-disease pair using enhanced or basic embeddings.

        Args:
            drug_id (str): The ID of the drug.
            disease_id (str): The ID of the disease.
            node_embedding_dict (dict): Pre-loaded dictionary of node_id to embedding vector.

        Returns:
            float: The predicted probability, or np.nan if prediction fails.
        """
        if not self.base_models_loaded_status or not self.stacking_model_loaded:
            logger.error("Models not loaded. Cannot predict single pair.")
            return np.nan

        try:
            # Create feature vector using optimized method when precomputed data is available
            pairs_data = [(drug_id, disease_id)]
            if self.precomputed_data_loaded:
                feature_vectors, valid_indices = self.create_features_for_pairs_optimized(pairs_data, node_embedding_dict)
            else:
                feature_vectors, valid_indices = self.create_features_for_pairs(pairs_data, node_embedding_dict)
            
            if len(valid_indices) == 0:
                logger.warning(f"Could not create features for pair {drug_id}-{disease_id}")
                return np.nan
                
            X_features_np = np.array(feature_vectors).reshape(1, -1)
            final_prob = self.predict_ensemble_for_features(X_features_np)
            return final_prob[0]
            
        except Exception as e:
            logger.error(f"Error during ensemble prediction for {drug_id}-{disease_id}: {e}", exc_info=True)
            return np.nan

    def predict_batch(self, pairs_df_or_list, node_embedding_dict: dict, drug_col='drug_id', dis_col='dis_id'):
        """
        Predicts indication probabilities for a batch of drug-disease pairs using enhanced or basic embeddings.

        Args:
            pairs_df_or_list: DataFrame or list of drug-disease pairs.
            node_embedding_dict (dict): Pre-loaded dictionary of node_id to embedding vector.
            drug_col (str): Column name for drug IDs (if DataFrame input).
            dis_col (str): Column name for disease IDs (if DataFrame input).

        Returns:
            np.ndarray: Array of predicted probabilities (NaN for failed predictions).
        """
        if not self.base_models_loaded_status or not self.stacking_model_loaded:
            logger.error("Models not loaded. Cannot predict batch.")
            return None

        # Standardize input format
        if hasattr(pairs_df_or_list, 'shape'):  # DataFrame-like
            num_input_pairs = pairs_df_or_list.shape[0]
            
            # Extract pairs data
            pairs_data = []
            for row_data in pairs_df_or_list.iter_rows(named=True):
                drug_id = row_data.get(drug_col)
                dis_id = row_data.get(dis_col)
                if drug_id is not None and dis_id is not None:
                    pairs_data.append((drug_id, dis_id))
                    
        elif isinstance(pairs_df_or_list, list):
            num_input_pairs = len(pairs_df_or_list)
            
            # Extract pairs data
            pairs_data = []
            for item in pairs_df_or_list:
                if isinstance(item, (tuple, list)) and len(item) >= 2:
                    pairs_data.append((item[0], item[1]))
                elif isinstance(item, dict):
                    drug_id = item.get(drug_col)
                    dis_id = item.get(dis_col)
                    if drug_id is not None and dis_id is not None:
                        pairs_data.append((drug_id, dis_id))
        else:
            logger.error("Invalid input type for pairs_df_or_list. Must be a DataFrame or list.")
            return None

        if not pairs_data:
            logger.warning("No valid pairs found in input.")
            return np.full(num_input_pairs, np.nan)

        try:
            # Create feature vectors using optimized method when precomputed data is available
            if self.precomputed_data_loaded:
                feature_vectors, valid_indices = self.create_features_for_pairs_optimized(pairs_data, node_embedding_dict)
            else:
                feature_vectors, valid_indices = self.create_features_for_pairs(pairs_data, node_embedding_dict)
            
            if not feature_vectors:
                logger.warning("No valid pairs with embeddings found in the batch.")
                return np.full(num_input_pairs, np.nan)

            # Make predictions
            X_features_batch_np = np.array(feature_vectors)
            batch_predictions = self.predict_ensemble_for_features(X_features_batch_np)

            # Map predictions back to original indices
            results = np.full(len(pairs_data), np.nan)
            for i, prediction in enumerate(batch_predictions):
                if i < len(valid_indices):
                    results[valid_indices[i]] = prediction

            logger.info(f"Successfully predicted {len(batch_predictions)} out of {len(pairs_data)} pairs in batch.")
            return results

        except Exception as e:
            logger.error(f"Error during batch ensemble prediction: {e}", exc_info=True)
            return np.full(num_input_pairs, np.nan)

def main():
    """
    Main function for command-line inference with enhanced embedding support.
    """
    parser = argparse.ArgumentParser(description="Drug-disease repurposing ensemble inference with enhanced embeddings")
    
    # Input options
    parser.add_argument('--drug_id', type=str, help='Single drug ID to predict')
    parser.add_argument('--disease_id', type=str, help='Single disease ID to predict')
    parser.add_argument('--pairs_file', type=str, help='File containing drug-disease pairs (CSV/parquet)')
    
    # Model paths
    parser.add_argument('--models_dir', type=str, default=MODELS_DIR, help='Directory containing trained models')
    parser.add_argument('--stacking_model_path', type=str, help='Path to stacking model (optional)')
    
    # Enhanced embedding parameters
    parser.add_argument('--use_enhanced_embeddings', action='store_true', default=True, 
                       help='Use enhanced embeddings (default: True)')
    parser.add_argument('--use_basic_embeddings', action='store_true', 
                       help='Force use of basic embeddings only')
    parser.add_argument('--k', type=int, default=5, 
                       help='Number of top similar nodes for enhanced embeddings (default: 5)')
    parser.add_argument('--exp_lambda', type=float, default=0.7, 
                       help='Lambda parameter for exponential rarity weighting (default: 0.7)')
    
    # Output
    parser.add_argument('--output_file', type=str, help='Output file for batch predictions')
    
    args = parser.parse_args()
    
    # Handle contradictory flags
    if args.use_basic_embeddings:
        args.use_enhanced_embeddings = False
    
    try:
        # Load node embeddings
        node_embedding_path = os.path.join(DATA_DIR, "processed_data", "node_embedding_dict.pkl")
        if not os.path.exists(node_embedding_path):
            raise FileNotFoundError(f"Node embeddings not found: {node_embedding_path}")
        
        with open(node_embedding_path, "rb") as f:
            node_embedding_dict = pickle.load(f)
        logger.info(f"Loaded {len(node_embedding_dict)} node embeddings")
        
        # Initialize predictor
        predictor = EnsemblePredictor(
            models_dir=args.models_dir,
            use_enhanced_embeddings=args.use_enhanced_embeddings,
            k=args.k,
            exp_lambda=args.exp_lambda
        )
        
        if args.stacking_model_path:
            predictor.load_stacking_model(args.stacking_model_path)
        
        # Single pair prediction
        if args.drug_id and args.disease_id:
            logger.info(f"Predicting single pair: {args.drug_id} - {args.disease_id}")
            prediction = predictor.predict_single_pair(args.drug_id, args.disease_id, node_embedding_dict)
            
            if np.isnan(prediction):
                print(f"Failed to predict for pair {args.drug_id} - {args.disease_id}")
            else:
                print(f"Prediction for {args.drug_id} - {args.disease_id}: {prediction:.6f}")
        
        # Batch prediction
        elif args.pairs_file:
            logger.info(f"Loading pairs from: {args.pairs_file}")
            
            if args.pairs_file.endswith('.parquet'):
                pairs_df = pl.read_parquet(args.pairs_file)
            else:
                pairs_df = pl.read_csv(args.pairs_file)
            
            logger.info(f"Loaded {pairs_df.shape[0]} pairs")
            
            # Predict
            predictions = predictor.predict_batch(pairs_df, node_embedding_dict)
            
            # Add predictions to DataFrame
            results_df = pairs_df.with_columns(
                pl.Series(name='prediction_probability', values=predictions)
            )
            
            if args.output_file:
                logger.info(f"Saving results to: {args.output_file}")
                if args.output_file.endswith('.parquet'):
                    results_df.write_parquet(args.output_file)
                else:
                    results_df.write_csv(args.output_file)
            else:
                print(results_df.head(10))
        
        else:
            parser.print_help()
            logger.error("Must specify either --drug_id and --disease_id for single prediction, or --pairs_file for batch prediction")
        
    except Exception as e:
        logger.error(f"Inference failed: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())


