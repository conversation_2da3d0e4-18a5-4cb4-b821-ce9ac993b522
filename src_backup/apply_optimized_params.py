"""
<PERSON><PERSON><PERSON> to apply optimized hyperparameters to the config file.

This script loads the optimization results from individual model files and updates 
the config.py file with the best parameters found during hyperparameter optimization.
"""

import os
import pickle
from pathlib import Path
import glob

from config import MODELS_DIR, LOGS_DIR
from utils import setup_logger, get_timestamp
from hyperparameter_optimization_base import find_latest_optimization_results, load_optimization_results

# Setup logging
timestamp = get_timestamp()
log_file = os.path.join(LOGS_DIR, f"apply_optimized_params_{timestamp}.log")
logger = setup_logger("apply_params", log_file)

def generate_config_content(results):
    """Generate the updated config.py content with optimized parameters."""
    
    # Read current config file
    config_path = os.path.join(Path(__file__).parent, "config.py")
    with open(config_path, "r") as f:
        lines = f.readlines()
    
    # Find the sections to replace
    new_lines = []
    skip_until = None
    
    for i, line in enumerate(lines):
        if skip_until and line.strip().startswith("}"):
            skip_until = None
            continue
        elif skip_until:
            continue
        
        # Check if we're at a parameter section that needs updating
        if "XGB_CLF_PARAMS = {" in line and results.get('xgb_clf'):
            new_lines.append(line)
            new_lines.extend(format_params_dict("XGB_CLF_PARAMS", results['xgb_clf']['best_params'], 'xgb_clf'))
            skip_until = "}"
        elif "LGBM_CLF_PARAMS = {" in line and results.get('lgbm_clf'):
            new_lines.append(line)
            new_lines.extend(format_params_dict("LGBM_CLF_PARAMS", results['lgbm_clf']['best_params'], 'lgbm_clf'))
            skip_until = "}"
        elif "CATBOOST_CLF_PARAMS = {" in line and results.get('catboost_clf'):
            new_lines.append(line)
            new_lines.extend(format_params_dict("CATBOOST_CLF_PARAMS", results['catboost_clf']['best_params'], 'catboost_clf'))
            skip_until = "}"
        elif "TABNET_CLF_PARAMS = {" in line and results.get('tabnet_clf'):
            new_lines.append(line)
            new_lines.extend(format_params_dict("TABNET_CLF_PARAMS", results['tabnet_clf']['best_params'], 'tabnet_clf'))
            skip_until = "}"
        else:
            new_lines.append(line)
    
    return ''.join(new_lines)

def format_params_dict(param_name, best_params, model_type):
    """Format the parameters dictionary for the config file."""
    lines = []
    
    if model_type == 'xgb_clf':
        lines.append('    "objective": "binary:logistic",\n')
        lines.append(f'    "max_depth": {best_params["max_depth"]},\n')
        lines.append(f'    "learning_rate": {best_params["learning_rate"]:.4f},\n')
        lines.append(f'    "n_estimators": {best_params["n_estimators"]},\n')
        lines.append('    "random_state": 42,\n')
        lines.append('    "scale_pos_weight": 10,\n')
        lines.append('    "early_stopping_rounds": 50,\n')
        lines.append('    "verbose_eval": False,\n')
        lines.append(f'    "reg_alpha": {best_params["reg_alpha"]:.3f},\n')
        lines.append(f'    "reg_lambda": {best_params["reg_lambda"]:.3f},\n')
        lines.append('    "min_child_weight": 3,\n')
        lines.append('    "gamma": 0.1,\n')
        lines.append('    "eval_metric": "logloss",\n')
        lines.append('    "n_jobs": 50,\n')
        lines.append('    "tree_method": "hist",\n')
        lines.append('    "device": f"cuda:{GPU_DEVICE_ID_XGB}" if USE_GPU else "cpu"\n')
    
    elif model_type == 'lgbm_clf':
        lines.append('    "objective": "binary",\n')
        lines.append(f'    "max_depth": {best_params["max_depth"]},\n')
        lines.append(f'    "learning_rate": {best_params["learning_rate"]:.4f},\n')
        lines.append(f'    "n_estimators": {best_params["n_estimators"]},\n')
        lines.append('    "random_state": 42,\n')
        lines.append('    "class_weight": "balanced",\n')
        lines.append('    "early_stopping_rounds": 50,\n')
        lines.append('    "verbose": -1,\n')
        lines.append(f'    "reg_alpha": {best_params["reg_alpha"]:.3f},\n')
        lines.append(f'    "reg_lambda": {best_params["reg_lambda"]:.3f},\n')
        lines.append('    "min_child_samples": 10,\n')
        lines.append('    "min_split_gain": 0.1,\n')
        lines.append('    "bagging_freq": 5,\n')
        lines.append('    "feature_fraction": 0.85,\n')
        lines.append('    "boost_from_average": True,\n')
        lines.append('    "n_jobs": 50,\n')
        lines.append('    "device_type": "gpu" if USE_GPU else "cpu",\n')
        lines.append('    "gpu_platform_id": GPU_DEVICE_ID_LGBM if USE_GPU else None,\n')
        lines.append('    "gpu_device_id": GPU_DEVICE_ID_LGBM if USE_GPU else None\n')
    
    elif model_type == 'catboost_clf':
        lines.append(f'    "iterations": {best_params["iterations"]},\n')
        lines.append(f'    "depth": {best_params["depth"]},\n')
        lines.append(f'    "learning_rate": {best_params["learning_rate"]:.4f},\n')
        lines.append('    "random_state": 42,\n')
        lines.append('    "loss_function": "Logloss",\n')
        lines.append('    "eval_metric": "PRAUC",\n')
        lines.append('    "task_type": "CPU",  # Use CPU for consistency with hyperparameter optimization\n')
        lines.append('    "thread_count": -1,  # Use all available CPU cores\n')
        lines.append('    "boost_from_average": True,  # Enable for better performance\n')
        lines.append('    "l2_leaf_reg": 3.0,  # L2 regularization\n')
        lines.append('    "random_strength": 1.0,  # Random strength for robustness\n')
        lines.append('    "bagging_temperature": 1.0,  # Bagging temperature\n')
        lines.append('    "verbose": False,\n')
        lines.append('    "early_stopping_rounds": 50,\n')
        lines.append('    "use_best_model": True,\n')
        lines.append('    "class_weights": [1, 10],  # Handle class imbalance\n')
        lines.append('    "border_count": 254,  # Reduced for faster training with large datasets\n')
        lines.append('    "allow_writing_files": False  # Disable CatBoost info files\n')
    
    elif model_type == 'tabnet_clf':
        lines.append('    # Architecture parameters - optimized values\n')
        lines.append(f'    "n_d": {best_params["n_d"]},                        # Width of the decision prediction layer (16-128)\n')
        lines.append(f'    "n_a": {best_params["n_a"]},                        # Width of the attention embedding (16-128)\n')
        lines.append(f'    "n_steps": {best_params["n_steps"]},                     # Number of steps in the architecture (3-10)\n')
        lines.append(f'    "gamma": {best_params["gamma"]:.3f},                     # Coefficient for feature reusage (1.0-2.0)\n')
        lines.append(f'    "n_independent": {best_params["n_independent"]},               # Number of independent GLU layers (1-5)\n')
        lines.append(f'    "n_shared": {best_params["n_shared"]},                    # Number of shared GLU layers (1-5)\n')
        lines.append(f'    "momentum": {best_params["momentum"]:.3f},                  # Momentum for batch normalization (0.01-0.4)\n')
        lines.append(f'    "lambda_sparse": {best_params["lambda_sparse"]:.6f},            # Sparsity loss coefficient (1e-5 to 1e-2)\n')
        lines.append(f'    "mask_type": "{best_params["mask_type"]}",         # Masking function (sparsemax or entmax)\n')
        lines.append('    \n')
        lines.append('    # Training parameters - optimized values\n')
        lines.append(f'    "max_epochs": {best_params["max_epochs"]},                # Maximum training epochs (50-300)\n')
        lines.append(f'    "patience": {best_params["patience"]},                   # Early stopping patience (10-30)\n')
        lines.append('    \n')
        lines.append('    # Batch parameters - optimized values (no longer fixed)\n')
        lines.append(f'    "batch_size": {best_params["batch_size"]},                # Batch size for training (256-1024)\n')
        lines.append(f'    "virtual_batch_size": {best_params["virtual_batch_size"]},        # Virtual batch size for batch normalization (64-512)\n')
        lines.append('    \n')
        lines.append('    # Other stable parameters\n')
        lines.append('    "verbose": 1,\n')
        lines.append('    "seed": SEED\n')
    
    lines.append('}\n')
    return lines

def backup_current_config():
    """Create a backup of the current config file."""
    config_path = os.path.join(Path(__file__).parent, "config.py")
    backup_path = os.path.join(Path(__file__).parent, f"config_backup_{timestamp}.py")
    
    with open(config_path, "r") as src, open(backup_path, "w") as dst:
        dst.write(src.read())
    
    logger.info(f"Current config backed up to: {backup_path}")
    return backup_path

def apply_optimized_parameters(results_files=None, backup=True):
    """Apply optimized parameters to the config file."""
    try:
        # Load optimization results
        results = load_optimization_results(results_files)
        
        # Create backup if requested
        if backup:
            backup_path = backup_current_config()
        
        # Generate new config content
        new_config_content = generate_config_content(results)
        
        # Write updated config
        config_path = os.path.join(Path(__file__).parent, "config.py")
        with open(config_path, "w") as f:
            f.write(new_config_content)
        
        logger.info("Successfully applied optimized parameters to config.py")
        
        # Print summary of applied parameters
        logger.info("\n" + "="*60)
        logger.info("APPLIED OPTIMIZED PARAMETERS SUMMARY")
        logger.info("="*60)
        
        for model_name, result in results.items():
            if result is not None:
                logger.info(f"\n{model_name.upper()}:")
                logger.info(f"  Best Score: {result['best_score']:.4f}")
                for param, value in result['best_params'].items():
                    if isinstance(value, float):
                        logger.info(f"  {param}: {value:.4f}")
                    else:
                        logger.info(f"  {param}: {value}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error applying optimized parameters: {e}")
        return False

def main():
    """Main function to apply optimized parameters."""
    logger.info("Starting application of optimized parameters from all 4 classifier model files...")
    logger.info("Note: All 4 classifier models must have optimization results to proceed.")
    
    success = apply_optimized_parameters()
    
    if success:
        logger.info("✓ Optimized parameters for all 4 classifier models successfully applied!")
        logger.info("You can now run training with the optimized configuration.")
    else:
        logger.error("✗ Failed to apply optimized parameters.")
    
    return success

if __name__ == "__main__":
    main() 