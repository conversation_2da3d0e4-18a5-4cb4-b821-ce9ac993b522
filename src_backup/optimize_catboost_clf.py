"""
Hyperparameter optimization for CatBoost Classifier.
"""

import os
import sys
import numpy as np
import gc

# Add src to path for imports first
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import average_precision_score
from skopt import gp_minimize
from skopt.space import Real, Integer, Categorical
from skopt.utils import use_named_args
import argparse
import catboost as cb

from hyperparameter_optimization_base import (
    load_prepared_data, save_optimization_results, 
    setup_logger, get_timestamp
)
from config import SEED, LOGS_DIR

# Setup logging
timestamp = get_timestamp()
os.makedirs(LOGS_DIR, exist_ok=True)
log_file = os.path.join(LOGS_DIR, f"optimize_catboost_clf_{timestamp}.log")
logger = setup_logger("catboost_clf_opt", log_file)

# Define search space for CatBoost - focus on most important parameters
# iterations, depth, learning_rate are the most critical for performance
SEARCH_SPACE = [
    Integer(100, 2500, name='iterations'),        # Number of boosting iterations
    Integer(4, 16, name='depth'),                 # Tree depth
    Real(0.01, 0.3, name='learning_rate'),        # Learning rate
]

# Fixed parameters
FIXED_PARAMS = {
    'task_type': 'CPU',
    'thread_count': -1,               # Use all CPU cores
    'boost_from_average': True,
    'l2_leaf_reg': 3.0,
    'random_strength': 1.0,
    'bagging_temperature': 1.0,
    'border_count': 254
}

def objective(X, y, weights, **params):
    """Objective function for CatBoost Classifier optimization."""
    try:
        cv = StratifiedKFold(n_splits=2, shuffle=True, random_state=SEED)  # Faster with 2-fold CV
        cv_splits = list(cv.split(X, y))
        
        scores = []
        
        for train_idx, val_idx in cv_splits:
            X_train_fold, X_val_fold = X[train_idx], X[val_idx]
            y_train_fold, y_val_fold = y[train_idx], y[val_idx]
            w_train_fold, w_val_fold = weights[train_idx], weights[val_idx]
            
            # Start with fixed parameters and add optimized ones
            model_params = FIXED_PARAMS.copy()
            model_params.update(params)
            model_params.update({
                'random_state': SEED,
                'verbose': False,  # Disable verbose output during optimization
                'allow_writing_files': False,  # Disable CatBoost info files
                'early_stopping_rounds': 50,  # Prevent overfitting
                'use_best_model': True  # Use best iteration for predictions
            })
            
            # Create CatBoost model
            model = cb.CatBoostClassifier(**model_params)
            
            # Fit the model with validation set for early stopping
            model.fit(
                X_train_fold, y_train_fold,
                sample_weight=w_train_fold,
                eval_set=(X_val_fold, y_val_fold),
                use_best_model=True,
                verbose=False
            )
            
            y_pred = model.predict_proba(X_val_fold)[:, 1]
            
            score = average_precision_score(y_val_fold, y_pred, sample_weight=w_val_fold)
            scores.append(score)
            
            # Explicit cleanup
            del model
            gc.collect()
        
        # Return negative score for minimization
        avg_score = np.mean(scores)
        return -avg_score
        
    except Exception as e:
        logger.warning(f"Error in objective function: {e}")
        return 0.0

def optimize_catboost_classifier(n_calls=30):
    """Optimize hyperparameters for CatBoost Classifier."""
    logger.info("Starting hyperparameter optimization for CatBoost Classifier...")
    
    # Load data
    X, y, weights = load_prepared_data()
    
    # Create objective function with @use_named_args decorator
    @use_named_args(SEARCH_SPACE)
    def objective_func(**params):
        return objective(X, y, weights, **params)
    
    # Perform Bayesian optimization
    result = gp_minimize(
        func=objective_func,
        dimensions=SEARCH_SPACE,
        n_calls=n_calls,
        n_initial_points=10,
        random_state=SEED,
        acq_func='EI',
        n_jobs=-1
    )
    
    # Extract best parameters
    best_params = {}
    for i, param_name in enumerate([dim.name for dim in SEARCH_SPACE]):
        best_params[param_name] = result.x[i]
    
    logger.info(f"Optimization completed for CatBoost Classifier")
    logger.info(f"Best score: {-result.fun:.4f}")
    logger.info(f"Best parameters: {best_params}")
    
    optimization_result = {
        'model_name': 'catboost_clf',
        'best_params': best_params,
        'best_score': -result.fun
    }
    
    # Save results
    save_optimization_results(optimization_result, 'catboost_clf')
    
    return optimization_result

def main():
    """Main entry point for command line execution."""
    parser = argparse.ArgumentParser(description="Optimize CatBoost Classifier hyperparameters")
    parser.add_argument("--n_calls", type=int, default=30, 
                                               help="Number of optimization calls (default: 30)")
    args = parser.parse_args()
    
    logger.info(f"Starting CatBoost Classifier optimization with {args.n_calls} calls...")
    
    # Run optimization with the specified n_calls
    result = optimize_catboost_classifier(n_calls=args.n_calls)
    
    logger.info("CatBoost Classifier optimization completed!")
    logger.info(f"Best score: {result['best_score']:.6f}")
    return True

if __name__ == "__main__":
    main() 