"""
TabNet utility functions for neural network-based tabular learning.

This module centralizes TabNet-related functionality including GPU device management,
parameter handling, and integration with the ensemble pipeline.
"""

import os
import logging
import torch
from config import USE_GPU, GPU_DEVICE_ID_TABNET
import numpy as np

# Global variable to track TabNet availability
TABNET_AVAILABLE = False

# Try importing TabNet
try:
    from pytorch_tabnet.tab_model import TabNetClassifier, TabNetRegressor
    TABNET_AVAILABLE = True
    logging.getLogger(__name__).info("TabNet imported successfully - GPU acceleration available")
except ImportError as e:
    logging.getLogger(__name__).warning(f"TabNet import failed: {e}. TabNet not available.")
    TABNET_AVAILABLE = False


def set_tabnet_device(gpu_id=None):
    """
    Set the GPU device for TabNet training.
    
    Args:
        gpu_id (int, optional): GPU device ID. If None, uses config default.
        
    Returns:
        str: Device string for TabNet ('auto', 'cpu', or 'cuda')
    """
    if not USE_GPU:
        logging.getLogger(__name__).info("GPU disabled via config - using CPU")
        return 'cpu'
    
    if not torch.cuda.is_available():
        logging.getLogger(__name__).warning("CUDA not available - falling back to CPU")
        return 'cpu'
        
    # Use provided GPU ID or default from config
    device_id = gpu_id if gpu_id is not None else GPU_DEVICE_ID_TABNET
    
    try:
        # Check if CUDA is available
        if torch.cuda.is_available():
            available_devices = torch.cuda.device_count()
            
            # Use specified device if available, otherwise fallback to device 0
            if device_id < available_devices:
                device_name = f'cuda:{device_id}'
                logging.getLogger(__name__).info(f"TabNet will use GPU device {device_id} ({device_name})")
                return device_name
            else:
                device_name = 'cuda:0'
                logging.getLogger(__name__).warning(f"GPU device {device_id} not available (only {available_devices} devices). Falling back to GPU 0")
                return device_name
        else:
            logging.getLogger(__name__).warning("CUDA not available. Using CPU")
            return 'cpu'
            
    except Exception as e:
        logging.getLogger(__name__).warning(f"Failed to set GPU device {device_id}: {e}")
        return 'cpu'


def get_tabnet_classifier():
    """
    Returns the TabNet Classifier if available.
    
    Returns:
        TabNetClassifier class if available, None otherwise
    """
    if TABNET_AVAILABLE:
        return TabNetClassifier
    else:
        logging.getLogger(__name__).warning("TabNet not available.")
        return None


def get_tabnet_regressor():
    """
    Returns the TabNet Regressor if available.
    
    Returns:
        TabNetRegressor class if available, None otherwise
    """
    if TABNET_AVAILABLE:
        return TabNetRegressor
    else:
        logging.getLogger(__name__).warning("TabNet not available.")
        return None


def is_tabnet_available():
    """
    Check if TabNet is available for use.
    
    Returns:
        bool: True if TabNet is available, False otherwise
    """
    return TABNET_AVAILABLE


def prepare_tabnet_params(params, gpu_id=None):
    """
    Prepare and validate TabNet parameters, including GPU device setting.
    Separates model initialization parameters from training parameters.
    
    Args:
        params (dict): Raw parameters dictionary
        gpu_id (int, optional): GPU device ID to use
        
    Returns:
        tuple: (model_params, training_params) - cleaned parameters for TabNet
    """
    if not TABNET_AVAILABLE:
        logging.getLogger(__name__).warning("TabNet not available")
        return None, None
    
    # Define which parameters belong to model initialization vs training
    MODEL_INIT_PARAMS = {
        'n_d', 'n_a', 'n_steps', 'gamma', 'n_independent', 'n_shared',
        'momentum', 'lambda_sparse', 'mask_type', 'seed', 'verbose'
    }
    
    TRAINING_PARAMS = {
        'max_epochs', 'patience', 'batch_size', 'virtual_batch_size'
    }
    
    # Convert numpy types to native Python types for TabNet compatibility
    model_params = {}
    training_params = {}
    
    for key, value in params.items():
        # Convert numpy types to Python native types
        if isinstance(value, (np.integer, np.int64, np.int32)):
            converted_value = int(value)
        elif isinstance(value, (np.floating, np.float64, np.float32)):
            converted_value = float(value)
        elif isinstance(value, (np.str_, str)):
            converted_value = str(value)
        else:
            converted_value = value
        
        # Separate model and training parameters
        if key in MODEL_INIT_PARAMS:
            model_params[key] = converted_value
        elif key in TRAINING_PARAMS:
            training_params[key] = converted_value
        else:
            # Default unknown parameters to model params (with warning)
            logging.getLogger(__name__).warning(f"Unknown TabNet parameter '{key}', adding to model params")
            model_params[key] = converted_value
    
    # Set device based on GPU configuration
    device = set_tabnet_device(gpu_id)
    model_params['device_name'] = device
    
    # Ensure virtual_batch_size <= batch_size (if both are present)
    batch_size = training_params.get('batch_size', 1024)
    virtual_batch_size = training_params.get('virtual_batch_size', 128)
    
    if virtual_batch_size > batch_size:
        training_params['virtual_batch_size'] = batch_size // 2
        logging.getLogger(__name__).info(f"Adjusted virtual_batch_size to {training_params['virtual_batch_size']}")
    
    # Remove any parameters that might conflict
    conflict_params = ['gpu_id', 'devices', 'device', 'gpu_device_id']
    for param in conflict_params:
        model_params.pop(param, None)
        training_params.pop(param, None)
    
    logging.getLogger(__name__).info(f"TabNet device configuration: {device}")
    logging.getLogger(__name__).info(f"Model params: {list(model_params.keys())}")
    logging.getLogger(__name__).info(f"Training params: {list(training_params.keys())}")
    
    return model_params, training_params


def create_tabnet_model(model_type='classifier', base_params=None, gpu_id=None, **optimization_params):
    """
    Create a TabNet model (classifier or regressor) with given parameters.
    
    Args:
        model_type (str): 'classifier' or 'regressor'
        base_params (dict): Base model parameters (optional)
        gpu_id (int, optional): GPU device ID to use
        **optimization_params: Additional parameters from optimization
        
    Returns:
        tuple: (TabNet model instance, training_params dict)
        
    Raises:
        RuntimeError: If TabNet is not available
    """
    if not TABNET_AVAILABLE:
        raise RuntimeError("TabNet not available")
    
    # Get the appropriate TabNet class
    if model_type == 'classifier':
        TabNetClass = get_tabnet_classifier()
    elif model_type == 'regressor':
        TabNetClass = get_tabnet_regressor()
    else:
        raise ValueError(f"Unknown model_type: {model_type}")
    
    if TabNetClass is None:
        raise RuntimeError("TabNet not available")
    
    # Combine base parameters with optimization parameters
    all_params = {}
    if base_params:
        all_params.update(base_params)
    all_params.update(optimization_params)
    
    # Prepare parameters including GPU device setting and separate model/training params
    model_params, training_params = prepare_tabnet_params(all_params, gpu_id)
    if model_params is None or training_params is None:
        raise RuntimeError("Failed to prepare TabNet parameters")
    
    # Create the model with only model initialization parameters
    model = TabNetClass(**model_params)
    
    logging.getLogger(__name__).info(f"Created TabNet {model_type} with device: {model_params['device_name']}")
    
    return model, training_params


def verify_tabnet_gpu():
    """
    Verify TabNet GPU setup and return device information.
    
    Returns:
        dict: GPU information and status
    """
    info = {
        'tabnet_available': TABNET_AVAILABLE,
        'cuda_available': torch.cuda.is_available() if TABNET_AVAILABLE else False,
        'device_count': torch.cuda.device_count() if TABNET_AVAILABLE and torch.cuda.is_available() else 0,
        'current_device': None,
        'configured_device': GPU_DEVICE_ID_TABNET if USE_GPU else None
    }
    
    if TABNET_AVAILABLE and torch.cuda.is_available():
        try:
            info['current_device'] = torch.cuda.current_device()
            info['device_name'] = torch.cuda.get_device_name()
        except Exception as e:
            logging.getLogger(__name__).warning(f"Error getting GPU info: {e}")
    
    return info 