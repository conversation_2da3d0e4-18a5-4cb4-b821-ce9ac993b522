# config.py
"""
Configuration settings for drug-disease repurposing ensemble model.
This file contains all configurable parameters used across the pipeline.
"""
import os
from pathlib import Path

# Directory paths
BASE_DIR = Path(__file__).parent.parent.absolute()
DATA_DIR = os.path.join(BASE_DIR, "data/")
MODELS_DIR = os.path.join(BASE_DIR, "models/")
LOGS_DIR = os.path.join(BASE_DIR, "logs/")
# Data parameters
EMBED_TYPE = "topological_embedding" # Options: "topological_embedding", "pca_embedding"

# Negative sampling parameters
SOFT_NEGATIVE_WEIGHT = 0.2
N_SOFT_NEG_PER_POS = 20

# Feature column name
DEFAULT_FEATURE_COL = "combined_embedding_vector"

# General
SEED = 42
VERBOSE = True  # Print detailed logs

# Processing Configuration
MAX_PROCESSES = 16  # Maximum number of processes to use (prevents excessive memory usage)

# GPU Configuration
USE_GPU = True  # Global flag to enable/disable GPU usage
GPU_DEVICE_ID_XGB = 3
GPU_DEVICE_ID_LGBM = 1
GPU_DEVICE_ID_CATBOOST = 0
GPU_DEVICE_ID_TABNET = 2

# Model parameters
# XGBoost
XGB_CLF_PARAMS = {
    "objective": "binary:logistic",
    "max_depth": 6,
    "learning_rate": 0.0536,
    "n_estimators": 1552,
    "random_state": 42,
    "scale_pos_weight": 10,
    "early_stopping_rounds": 50,
    "verbose_eval": False,
    "reg_alpha": 4.546,
    "reg_lambda": 3.397,
    "min_child_weight": 3,
    "gamma": 0.1,
    "eval_metric": "logloss",
    "n_jobs": 50,
    "tree_method": "hist",
    "device": f"cuda:{GPU_DEVICE_ID_XGB}" if USE_GPU else "cpu"
}

# LightGBM
LGBM_CLF_PARAMS = {
    "objective": "binary",
    "max_depth": 7,
    "learning_rate": 0.0587,
    "n_estimators": 1856,
    "random_state": 42,
    "class_weight": "balanced",
    "early_stopping_rounds": 50,
    "verbose": -1,
    "reg_alpha": 0.000,
    "reg_lambda": 8.566,
    "min_child_samples": 10,
    "min_split_gain": 0.1,
    "bagging_freq": 5,
    "feature_fraction": 0.85,
    "boost_from_average": True,
    "n_jobs": 50,
    "device_type": "gpu" if USE_GPU else "cpu",
    "gpu_platform_id": GPU_DEVICE_ID_LGBM if USE_GPU else None,
    "gpu_device_id": GPU_DEVICE_ID_LGBM if USE_GPU else None
}

# CatBoost
CATBOOST_CLF_PARAMS = {
    "iterations": 2500,
    "depth": 6,
    "learning_rate": 0.1603,
    "random_state": 42,
    "loss_function": "Logloss",
    "eval_metric": "PRAUC",
    "task_type": "CPU",  # Use CPU for consistency with hyperparameter optimization
    "thread_count": -1,  # Use all available CPU cores
    "boost_from_average": True,  # Enable for better performance
    "l2_leaf_reg": 3.0,  # L2 regularization
    "random_strength": 1.0,  # Random strength for robustness
    "bagging_temperature": 1.0,  # Bagging temperature
    "verbose": False,
    "early_stopping_rounds": 50,
    "use_best_model": True,
    "class_weights": [1, 10],  # Handle class imbalance
    "border_count": 254,  # Reduced for faster training with large datasets
    "allow_writing_files": False  # Disable CatBoost info files
}

# TabNet - Neural network for tabular data
TABNET_CLF_PARAMS = {
    # Architecture parameters - optimized values
    "n_d": 59,                        # Width of the decision prediction layer (16-128)
    "n_a": 126,                        # Width of the attention embedding (16-128)
    "n_steps": 6,                     # Number of steps in the architecture (3-10)
    "gamma": 1.860,                     # Coefficient for feature reusage (1.0-2.0)
    "n_independent": 4,               # Number of independent GLU layers (1-5)
    "n_shared": 3,                    # Number of shared GLU layers (1-5)
    "momentum": 0.015,                  # Momentum for batch normalization (0.01-0.4)
    "lambda_sparse": 0.006708,            # Sparsity loss coefficient (1e-5 to 1e-2)
    "mask_type": "entmax",         # Masking function (sparsemax or entmax)
    
    # Training parameters - optimized values
    "max_epochs": 146,                # Maximum training epochs (50-300)
    "patience": 15,                   # Early stopping patience (10-30)
    
    # Batch parameters - optimized values (no longer fixed)
    "batch_size": 268,                # Batch size for training (256-1024)
    "virtual_batch_size": 167,        # Virtual batch size for batch normalization (64-512)
    
    # Other stable parameters
    "verbose": 1,
    "seed": SEED
}

# Meta-learner parameters
META_MODEL_TYPE = "logreg"  # Options: "logreg", "lgbm"
META_LOGREG_PARAMS = {
    "C": 1.0,
    "class_weight": "balanced",
    "max_iter": 1000,
    "random_state": 42
}

META_LGBM_PARAMS = {
    "objective": "binary",
    "max_depth": 3,
    "learning_rate": 0.05,
    "n_estimators": 100,
    "random_state": 42
}

# Evaluation parameters
EVAL_METRICS = ["acc", "f1", "precision", "recall", "pr_auc", "mrr", "hit@5", "hit@10", "hit@100"]


