"""
Hyperparameter optimization for XGBoost Classifier.
"""

import os
import sys
import numpy as np
import xgboost as xgb
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import average_precision_score
from skopt import gp_minimize
from skopt.space import Real, Integer
from skopt.utils import use_named_args
import argparse

# Add src to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from hyperparameter_optimization_base import (
    load_prepared_data, save_optimization_results, 
    setup_logger, get_timestamp
)
from config import SEED, XGB_CLF_PARAMS, LOGS_DIR

# Setup logging
timestamp = get_timestamp()
os.makedirs(LOGS_DIR, exist_ok=True)
log_file = os.path.join(LOGS_DIR, f"optimize_xgb_clf_{timestamp}.log")
logger = setup_logger("xgb_clf_opt", log_file)

# Define search space
SEARCH_SPACE = [
    Real(0.01, 0.3, name='learning_rate'),
    Integer(3, 20, name='max_depth'),
    Real(0.0, 10.0, name='reg_lambda'),
    Real(0.0, 10.0, name='reg_alpha'),
    Integer(100, 2500, name='n_estimators')
]

def objective(X, y, weights, **params):
    """Objective function for XGBoost Classifier optimization."""
    try:
        cv = StratifiedKFold(n_splits=2, shuffle=True, random_state=SEED)
        cv_splits = list(cv.split(X, y))
        
        scores = []
        
        for train_idx, val_idx in cv_splits:
            X_train_fold, X_val_fold = X[train_idx], X[val_idx]
            y_train_fold, y_val_fold = y[train_idx], y[val_idx]
            w_train_fold, w_val_fold = weights[train_idx], weights[val_idx]
            
            # Start with base parameters and update with optimized ones
            model_params = XGB_CLF_PARAMS.copy()
            model_params.update(params)
            model_params.update({
                'early_stopping_rounds': 50,
                'random_state': SEED,
                'eval_metric': 'logloss'
            })
            
            model = xgb.XGBClassifier(**model_params)
            model.fit(X_train_fold, y_train_fold, 
                     sample_weight=w_train_fold,
                     eval_set=[(X_val_fold, y_val_fold)],
                     sample_weight_eval_set=[w_val_fold],
                     verbose=False)
            y_pred = model.predict_proba(X_val_fold)[:, 1]
            score = average_precision_score(y_val_fold, y_pred, sample_weight=w_val_fold)
            scores.append(score)
        
        # Return negative score for minimization
        avg_score = np.mean(scores)
        return -avg_score
        
    except Exception as e:
        logger.warning(f"Error in objective function: {e}")
        return 0.0

def optimize_xgb_classifier(n_calls=30):
    """Optimize hyperparameters for XGBoost Classifier."""
    logger.info("Starting hyperparameter optimization for XGBoost Classifier...")
    
    # Load data
    X, y, weights = load_prepared_data()
    
    # Create objective function with @use_named_args decorator
    @use_named_args(SEARCH_SPACE)
    def objective_func(**params):
        return objective(X, y, weights, **params)
    
    # Perform Bayesian optimization
    result = gp_minimize(
        func=objective_func,
        dimensions=SEARCH_SPACE,
        n_calls=n_calls,
        n_initial_points=10,
        random_state=SEED,
        acq_func='EI',
        n_jobs=-1
    )
    
    # Extract best parameters
    best_params = {}
    for i, param_name in enumerate([dim.name for dim in SEARCH_SPACE]):
        best_params[param_name] = result.x[i]
    
    logger.info(f"Optimization completed for XGBoost Classifier")
    logger.info(f"Best score: {-result.fun:.4f}")
    logger.info(f"Best parameters: {best_params}")
    
    optimization_result = {
        'model_name': 'xgb_clf',
        'best_params': best_params,
        'best_score': -result.fun
    }
    
    # Save results
    save_optimization_results(optimization_result, 'xgb_clf')
    
    return optimization_result

def main():
    """Main entry point for command line execution."""
    parser = argparse.ArgumentParser(description="Optimize XGBoost Classifier hyperparameters")
    parser.add_argument("--n_calls", type=int, default=30, 
                                               help="Number of optimization calls (default: 30)")
    args = parser.parse_args()
    
    logger.info(f"Starting XGBoost Classifier optimization with {args.n_calls} calls...")
    
    # Run optimization with the specified n_calls
    result = optimize_xgb_classifier(n_calls=args.n_calls)
    
    logger.info("XGBoost Classifier optimization completed!")
    logger.info(f"Best score: {result['best_score']:.6f}")
    return True

if __name__ == "__main__":
    main() 