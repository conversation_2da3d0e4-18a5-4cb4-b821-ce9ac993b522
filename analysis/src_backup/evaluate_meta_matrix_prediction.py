"""
Generates new predictions for matrix_predictions.parquet using the multi-level stacking model.

This script:
1. Loads the trained multi-level stacking model
2. Precomputes expensive enhanced embedding operations once for efficiency
3. Loads the matrix_predictions.parquet file in batches to reduce memory usage
4. Uses the EnsemblePredictor to generate new predictions to replace the 'treat score' column
5. Drops specified columns and saves the updated matrix

Usage:
    python evaluate_meta_matrix_prediction.py --output_path updated_matrix_stacking.parquet
    python evaluate_meta_matrix_prediction.py --stacking_model_path /path/to/stacking_model.joblib --output_path updated_matrix_stacking.parquet --batch_size 5000
"""

import os
import sys
import numpy as np
import polars as pl
import argparse
from joblib import load as joblib_load
import pickle
from tqdm import trange

# Add the src directory to path to import modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from inference import EnsemblePredictor
from config import MODELS_DIR, DATA_DIR, SEED, LOGS_DIR
from utils import setup_logger, get_timestamp

# Setup logging
timestamp = get_timestamp()
os.makedirs(LOGS_DIR, exist_ok=True)
log_file = os.path.join(LOGS_DIR, f"evaluate_meta_matrix_{timestamp}.log")
logger = setup_logger("evaluate_meta_matrix", log_file)

# Setup paths
MATRIX_PREDICTIONS_PATH = os.path.join(DATA_DIR, "raw_data", "matrix_predictions.parquet")

np.random.seed(SEED)

# Columns to drop from the matrix
COLUMNS_TO_DROP = [
    'trial_sig_better', 'trial_non_sig_better', 'trial_sig_worse', 'trial_non_sig_worse',
    'not treat score', 'unknown score', 'rank', 'quantile_rank', '__index_level_0__'
]

def load_stacking_model(model_path=None):
    """Load the multi-level stacking model."""
    if model_path:
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Stacking model not found: {model_path}")
        logger.info(f"Loading custom stacking model from: {model_path}")
        return joblib_load(model_path)
    
    # Default stacking model path
    default_path = os.path.join(MODELS_DIR, "multi_level_stacking_model.joblib")
    if not os.path.exists(default_path):
        raise FileNotFoundError(f"Default stacking model not found: {default_path}")
    
    logger.info(f"Loading default stacking model from: {default_path}")
    return joblib_load(default_path)

def process_matrix_in_batches(matrix_path, predictor, node_embedding_dict, output_path, batch_size=10000):
    """
    Process the matrix in batches to reduce memory consumption.
    
    Args:
        matrix_path (str): Path to matrix_predictions.parquet
        predictor: EnsemblePredictor instance
        node_embedding_dict (dict): Dictionary mapping node IDs to embeddings
        output_path (str): Output path for updated matrix
        batch_size (int): Number of rows to process in each batch
    """
    logger.info(f"Processing matrix in batches of {batch_size} rows...")
    
    # Get total number of rows first
    total_rows = pl.scan_parquet(matrix_path).select(pl.len()).collect().item()
    logger.info(f"Total rows to process: {total_rows}")
    
    processed_batches = []
    valid_pairs_count = 0
    
    # Process in batches
    for batch_start in trange(0, total_rows, batch_size):
        batch_end = min(batch_start + batch_size, total_rows)
        batch_num = batch_start // batch_size + 1
        total_batches = (total_rows + batch_size - 1) // batch_size
        
        logger.info(f"\nProcessing batch {batch_num}/{total_batches} (rows {batch_start}-{batch_end-1})...")
        
        # Load batch
        batch_df = pl.scan_parquet(matrix_path).slice(batch_start, batch_size).collect()
        logger.info(f"Loaded batch: {batch_df.shape}")
        
        # Create standardized pairs DataFrame for EnsemblePredictor
        pairs_df = batch_df.select([
            pl.col('source').alias('drug_id'),
            pl.col('target').alias('dis_id')
        ])
        
        # Generate predictions using the predictor's built-in feature generation
        logger.info(f"Batch {batch_num}: Generating ensemble predictions with enhanced embeddings...")
        predictions = predictor.predict_batch(pairs_df, node_embedding_dict, drug_col='drug_id', dis_col='dis_id')
        
        # Filter out failed predictions and corresponding matrix rows
        valid_mask = ~np.isnan(predictions)
        valid_indices = np.where(valid_mask)[0]
        
        if len(valid_indices) < len(predictions):
            logger.warning(f"Batch {batch_num}: Removing {len(predictions) - len(valid_indices)} rows with failed predictions")
            batch_df = batch_df.with_row_index().filter(pl.col("index").is_in(valid_indices.tolist())).drop("index")
            predictions = predictions[valid_mask]
        
        logger.info(f"Batch {batch_num}: Generated predictions for {len(predictions)} drug-disease pairs")
        valid_pairs_count += len(predictions)
        
        # Replace 'treat score' column with new predictions
        batch_with_predictions = batch_df.with_columns(
            pl.Series(name='treat score', values=predictions)
        )
        
        # Drop specified columns
        columns_to_drop_final = [col for col in COLUMNS_TO_DROP if col in batch_with_predictions.columns]
        if columns_to_drop_final:
            final_batch = batch_with_predictions.drop(columns_to_drop_final)
        else:
            final_batch = batch_with_predictions
        
        processed_batches.append(final_batch)
        logger.info(f"Batch {batch_num}: Processed {final_batch.shape[0]} pairs successfully")
    
    # Combine all processed batches
    if not processed_batches:
        raise ValueError("No valid batches were processed!")
    
    logger.info(f"\nCombining {len(processed_batches)} processed batches...")
    final_matrix = pl.concat(processed_batches)
    
    logger.info(f"Total valid pairs processed: {valid_pairs_count}")
    logger.info(f"Final matrix shape: {final_matrix.shape}")
    
    # Save updated matrix
    logger.info(f"Saving updated matrix to: {output_path}")
    os.makedirs(os.path.dirname(output_path) if os.path.dirname(output_path) else '.', exist_ok=True)
    final_matrix.write_parquet(output_path)
    
    return final_matrix

def main():
    """Main function to generate new predictions for matrix using multi-level stacking model with enhanced embeddings."""
    parser = argparse.ArgumentParser(description="Generate predictions for matrix using multi-level stacking model with enhanced embeddings")
    
    # Arguments
    parser.add_argument('--output_path', required=True, help='Output path for updated matrix')
    parser.add_argument('--stacking_model_path', help='Path to custom stacking model file (optional)')
    parser.add_argument('--batch_size', type=int, default=10000, help='Batch size for processing (default: 10000)')
    parser.add_argument('--k', type=int, default=5, help='Number of top similar nodes for enhanced embeddings (default: 5)')
    parser.add_argument('--exp_lambda', type=float, default=0.7, help='Lambda parameter for exponential rarity weighting (default: 0.7)')
    
    args = parser.parse_args()
    
    try:
        # Load the stacking model
        stacking_model = load_stacking_model(args.stacking_model_path)
        
        # Load node embeddings
        node_embedding_path = os.path.join(DATA_DIR, "processed_data", "node_embedding_dict.pkl")
        with open(node_embedding_path, "rb") as f:
            node_embedding_dict = pickle.load(f)
        logger.info(f"Loaded {len(node_embedding_dict)} node embeddings")
        
        # Check if matrix predictions file exists
        logger.info(f"Matrix predictions file: {MATRIX_PREDICTIONS_PATH}")
        if not os.path.exists(MATRIX_PREDICTIONS_PATH):
            raise FileNotFoundError(f"Matrix predictions file not found: {MATRIX_PREDICTIONS_PATH}")
        
        # Initialize the ensemble predictor with enhanced embeddings
        logger.info("Initializing ensemble predictor with enhanced embeddings...")
        predictor = EnsemblePredictor(
            stacking_model=stacking_model,
            use_enhanced_embeddings=True,
            k=args.k,
            exp_lambda=args.exp_lambda
        )
        
        # Load base models (required for ensemble prediction)
        logger.info("Loading base models...")
        predictor.load_base_models()
        
        # Precompute expensive operations once for batch efficiency
        logger.info("Precomputing enhanced embedding data for batch efficiency...")
        predictor.precompute_enhanced_embedding_data(node_embedding_dict)
        
        # Process matrix in batches for memory efficiency
        logger.info("Using batch processing mode for memory efficiency...")
        final_matrix = process_matrix_in_batches(
            MATRIX_PREDICTIONS_PATH, predictor, node_embedding_dict,
            args.output_path, batch_size=args.batch_size
        )
        
        logger.info("Matrix evaluation completed successfully!")
        logger.info(f"Final matrix shape: {final_matrix.shape}")
        logger.info(f"Updated matrix saved to: {args.output_path}")
        
    except Exception as e:
        logger.error(f"Error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main()) 