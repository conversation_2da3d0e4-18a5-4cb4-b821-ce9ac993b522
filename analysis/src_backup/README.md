# Model Evaluation Scripts

This directory contains scripts for evaluating individual base models and the multi-level stacking ensemble model on the test set.

## Scripts

### evaluate_base_matrix_prediction.py
Generates new predictions for `matrix_predictions.parquet` using a specific base model.

**Usage:**
```bash
# Using predefined model names
python evaluate_base_matrix_prediction.py --model_name xgb_clf --output_path matrix_xgb_clf.parquet
python evaluate_base_matrix_prediction.py --model_name lgbm_clf --output_path matrix_lgbm_clf.parquet
python evaluate_base_matrix_prediction.py --model_name catboost_clf --output_path matrix_catboost.parquet
python evaluate_base_matrix_prediction.py --model_name tabnet_clf --output_path matrix_tabnet.parquet

# Using custom model paths
python evaluate_base_matrix_prediction.py --model_path /path/to/custom_model.joblib --output_path matrix_custom.parquet

# Specify custom matrix path
python evaluate_base_matrix_prediction.py --model_name xgb_clf --matrix_path /path/to/matrix.parquet --output_path matrix_xgb_clf.parquet
```

**Available predefined models:**
- `xgb_clf`: XGBoost Classifier
- `lgbm_clf`: LightGBM Classifier 
- `catboost_clf`: CatBoost Classifier
- `tabnet_clf`: TabNet Classifier

**Parameters:**
- `--model_name`: Use a predefined model from the default models directory
- `--model_path`: Use a custom path to a model file (.joblib)
- `--output_path`: Required output path for the updated matrix file
- `--matrix_path`: Optional custom path to matrix_predictions.parquet (defaults to data/raw_data/matrix_predictions.parquet)
- Note: `--model_name` and `--model_path` are mutually exclusive

### evaluate_meta_matrix_prediction.py
Generates new predictions for `matrix_predictions.parquet` using the multi-level stacking ensemble model.

**Usage:**
```bash
# Using default stacking model
python evaluate_meta_matrix_prediction.py --output_path matrix_ensemble.parquet

# Using custom stacking model
python evaluate_meta_matrix_prediction.py --stacking_model_path /path/to/stacking_model.joblib --output_path matrix_ensemble.parquet
```

**Parameters:**
- `--stacking_model_path`: Optional custom path to multi-level stacking model
- `--output_path`: Required output path for the updated matrix file
- `--matrix_path`: Optional custom path to matrix_predictions.parquet

## Output

Both scripts will:
1. Load the matrix data or test set from the prepared dataset
2. Make predictions using the specified model
3. Calculate comprehensive evaluation metrics including:
   - Classification metrics (Accuracy, Precision, Recall, F1, PR-AUC, LogLoss)
   - Ranking metrics (MRR, Hit@K, Recall@N)
4. Save logs to `../logs/{model_name}_{timestamp}.log`

## Requirements

Make sure you have:
1. Trained base models in `../../models/base_models/`
2. Trained multi-level stacking model in `../../models/stacking_model.joblib`
3. Prepared dataset with test split
4. All required dependencies installed

## Matrix Prediction Scripts

The matrix prediction scripts work with the large `matrix_predictions.parquet` file and:
1. Load the specified model (base classifier or multi-level stacking ensemble)
2. Generate features from drug-disease pairs using node embeddings
3. Make predictions to replace the 'treat score' column
4. Drop unnecessary columns: `trial_sig_better`, `trial_non_sig_better`, `trial_sig_worse`, `trial_non_sig_worse`, `not treat score`, `unknown score`, `rank`, `quantile_rank`, `__index_level_0__`
5. Save the updated matrix to the specified output path

## Example Commands

```bash
# Navigate to the analysis/src directory
cd analysis/src

# Generate predictions for matrix using base models
python evaluate_base_matrix_prediction.py --model_name xgb_clf --output_path matrix_xgb_clf.parquet
python evaluate_base_matrix_prediction.py --model_name lgbm_clf --output_path matrix_lgbm_clf.parquet
python evaluate_base_matrix_prediction.py --model_name catboost_clf --output_path matrix_catboost.parquet
python evaluate_base_matrix_prediction.py --model_name tabnet_clf --output_path matrix_tabnet.parquet

# Generate predictions for matrix using multi-level stacking ensemble
python evaluate_meta_matrix_prediction.py --output_path matrix_ensemble.parquet
```

## Multi-Level Stacking Architecture

The ensemble model uses a sophisticated multi-level stacking approach:
- **Level 1 (Base Models)**: 4 diverse tree-based classifiers
- **Level 2 (Meta-Learners)**: Gaussian Process, Neural Network (MLP), and Confidence-weighted ensemble
- **Level 3 (Final Combination)**: Logistic Regression combines base predictions and Level 2 outputs

This architecture ensures maximum diversity and robust predictions across different types of drug-disease relationships. 