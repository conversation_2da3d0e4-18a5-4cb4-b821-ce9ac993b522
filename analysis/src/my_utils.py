"""
Utility functions for matrix prediction analysis and evaluation.

"""
import logging
from utils import load_df_with_caching, join_off_label_to_matrix
import polars as pl
from tqdm import tqdm
import numpy as np

from evaluation import (
    give_recall_at_n,
    give_hit_at_k,
    give_disease_specific_mrr
)
from sklearn.metrics import (
    precision_score, recall_score, f1_score,
    average_precision_score
)
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np


# Configure logging to output to console only
logging.basicConfig(
    level=logging.INFO,
    format='%(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
BUCKET_WG1 = 'mtrx-us-central1-wg1-data-dev-storage'
BUCKET_NAME = 'mtrx-us-central1-hub-dev-storage'
OFFLABEL_PATH = 'piotr/prime_kg_pairs/off_label_edges_normalized.parquet'

def preprocess_matrix_prediction(psu_matrix_path1, psu_matrix_path2=None, client=None, version="v0.6.8", fold=0):
    """
    Preprocess matrix predictions for analysis and evaluation.

    Args:
        psu_matrix_path1 (str): Path to the first PSU matrix file
        psu_matrix_path2 (str, optional): Path to the second PSU matrix file. If provided,
            the function will compare two PSU matrices instead of EC vs PSU
        client: Google Cloud Storage client (required for Scenario 2 when loading EC baseline)
        version (str): EC baseline version ("v0.4.5" or "v0.6.8") - only used in Scenario 2
        fold (int): Fold number for EC baseline - only used in Scenario 2

    Returns:
        tuple:
            - Scenario 1 (psu_matrix_path2 provided): (PSU1_filtered, PSU2_filtered)
            - Scenario 2 (psu_matrix_path2 is None): (EC_filtered, PSU_filtered)
    """

    # Parameter validation
    if psu_matrix_path1 is None:
        raise ValueError("psu_matrix_path1 cannot be None")

    logging.info(f"Starting preprocessing with version={version}, fold={fold}")
    logging.info(f"PSU matrix path 1: {psu_matrix_path1}")

    if psu_matrix_path2 is not None:
        # Scenario 1: Compare two PSU matrices
        logging.info(f"PSU matrix path 2: {psu_matrix_path2}")
        logging.info("Scenario 1: Comparing two PSU matrices (skipping EC baseline)")

        if client is None:
            raise ValueError("client parameter is required for loading off-label data")

        # Load both PSU matrix predictions
        logging.info("Loading first PSU matrix predictions from file...")
        PSU1_matrix_predictions = pl.read_parquet(psu_matrix_path1)

        logging.info("Loading second PSU matrix predictions from file...")
        PSU2_matrix_predictions = pl.read_parquet(psu_matrix_path2)

        # Find common drug-disease pairs between the two PSU matrices
        logging.info("Finding common drug-disease pairs between PSU matrices...")
        PSU1_dd_pairs = {(drug_id, disease_id): 1 for drug_id, disease_id in tqdm(zip(PSU1_matrix_predictions['source'], PSU1_matrix_predictions['target']), total=len(PSU1_matrix_predictions))}
        logging.info(f"PSU1 unique drug-disease pairs: {len(PSU1_dd_pairs)}")

        PSU2_dd_pairs = {(drug_id, disease_id): 1 for drug_id, disease_id in tqdm(zip(PSU2_matrix_predictions['source'], PSU2_matrix_predictions['target']), total=len(PSU2_matrix_predictions))}
        logging.info(f"PSU2 unique drug-disease pairs: {len(PSU2_dd_pairs)}")

        common_drug_disease_pairs = []
        for drug_id, disease_id in tqdm(zip(PSU1_matrix_predictions['source'], PSU1_matrix_predictions['target']), total=len(PSU1_matrix_predictions)):
            if (drug_id, disease_id) in PSU1_dd_pairs and (drug_id, disease_id) in PSU2_dd_pairs:
                common_drug_disease_pairs.append((drug_id, disease_id))

        logging.info(f"Common drug-disease pairs found: {len(common_drug_disease_pairs)}")

        # Filter both PSU matrices to only include common pairs
        common_pairs_df = pl.DataFrame({
            'source': [pair[0] for pair in common_drug_disease_pairs],
            'target': [pair[1] for pair in common_drug_disease_pairs]
        })

        logging.info("Filtering first PSU matrix predictions...")
        PSU1_filtered = PSU1_matrix_predictions.join(
            common_pairs_df,
            on=['source', 'target'],
            how='inner'
        )[['source', 'target', 'is_known_positive', 'is_known_negative', 'treat score']]

        logging.info("Filtering second PSU matrix predictions...")
        PSU2_filtered = PSU2_matrix_predictions.join(
            common_pairs_df,
            on=['source', 'target'],
            how='inner'
        )[['source', 'target', 'is_known_positive', 'is_known_negative', 'treat score']]

        # Set up Off-label data
        logging.info("Loading off-label data...")
        bucket_wg1 = client.bucket(BUCKET_WG1)
        off_label_edges = load_df_with_caching(OFFLABEL_PATH, "off_label_edges", bucket_wg1)

        # Apply off-label data joining to both filtered PSU matrices
        logging.info("Joining off-label data with first PSU filtered data...")
        PSU1_filtered = join_off_label_to_matrix(off_label_edges, PSU1_filtered)
        logging.info(f"PSU1 filtered with off-label: {PSU1_filtered.shape}")

        logging.info("Joining off-label data with second PSU filtered data...")
        PSU2_filtered = join_off_label_to_matrix(off_label_edges, PSU2_filtered)
        logging.info(f"PSU2 filtered with off-label: {PSU2_filtered.shape}")

        logging.info("Preprocessing completed successfully!")
        return PSU1_filtered, PSU2_filtered

    else:
        # Scenario 2: Compare EC baseline with single PSU matrix (original behavior)
        logging.info("Scenario 2: Comparing EC baseline with PSU matrix")

        if client is None:
            raise ValueError("client parameter is required for loading EC baseline model")

        if version == "v0.4.5":
            RUN_PATH = "kedro/data/releases/v0.4.5/runs/045-modelling-run-75339eb5/"
            cache_name = f'045-modelling-run-75339eb5_matrix_predictions_fold_{fold}'
        elif version == "v0.6.8":
            RUN_PATH = 'kedro/data/releases/v0.6.8/runs/txgnn-8-3ce4ab2d/'
            cache_name = f'068-modelling-run-txgnn-8-3ce4ab2d_matrix_predictions_fold_{fold}'
        else:
            raise ValueError(f"Invalid version: {version}. Only v0.4.5 and v0.6.8 are supported.")

        MATRIX_PATH = RUN_PATH + f'datasets/matrix_generation/model_output/fold_{fold}/matrix_predictions'
        logging.info(f"EC matrix path: {MATRIX_PATH}")

        # Load matrix prediction based on specified EC baseline version
        logging.info("Loading EC matrix predictions from bucket...")
        bucket = client.bucket(BUCKET_NAME)
        EC_matrix_predictions = load_df_with_caching(MATRIX_PATH, cache_name, bucket)

        # Load PSU matrix prediction
        logging.info("Loading PSU matrix predictions from file...")
        PSU_matrix_predictions = pl.read_parquet(psu_matrix_path1)

        # Find the common drug-disease pairs in the EC's matrix prediction and the PSU models
        logging.info("Finding common drug-disease pairs...")
        EC_dd_pairs = {(drug_id, disease_id): 1 for drug_id, disease_id in tqdm(zip(EC_matrix_predictions['source'], EC_matrix_predictions['target']), total=len(EC_matrix_predictions))}
        logging.info(f"EC unique drug-disease pairs: {len(EC_dd_pairs)}")

        PSU_dd_pairs = {(drug_id, disease_id): 1 for drug_id, disease_id in tqdm(zip(PSU_matrix_predictions['source'], PSU_matrix_predictions['target']), total=len(PSU_matrix_predictions))}
        logging.info(f"PSU unique drug-disease pairs: {len(PSU_dd_pairs)}")

        common_drug_disease_pairs = []
        for drug_id, disease_id in tqdm(zip(EC_matrix_predictions['source'], EC_matrix_predictions['target']), total=len(EC_matrix_predictions)):
            if (drug_id, disease_id) in EC_dd_pairs and (drug_id, disease_id) in PSU_dd_pairs:
                common_drug_disease_pairs.append((drug_id, disease_id))

        logging.info(f"Common drug-disease pairs found: {len(common_drug_disease_pairs)}")

        # Filter each matrix prediction to only include the common drug-disease pairs
        common_pairs_df = pl.DataFrame({
            'source': [pair[0] for pair in common_drug_disease_pairs],
            'target': [pair[1] for pair in common_drug_disease_pairs]
        })

        logging.info("Filtering EC matrix predictions...")
        EC_filtered = EC_matrix_predictions.join(
            common_pairs_df,
            on=['source', 'target'],
            how='inner'
        )[['source', 'target', 'is_known_positive', 'is_known_negative', 'treat score']]

        logging.info("Filtering PSU matrix predictions...")
        PSU_filtered = PSU_matrix_predictions.join(
            common_pairs_df,
            on=['source', 'target'],
            how='inner'
        )[['source', 'target', 'is_known_positive', 'is_known_negative', 'treat score']]

        # Set up Off-label data
        logging.info("Loading off-label data...")
        bucket_wg1 = client.bucket(BUCKET_WG1)
        off_label_edges = load_df_with_caching(OFFLABEL_PATH, "off_label_edges", bucket_wg1)

        # Merge off-label edges with matrix predictions
        logging.info("Joining off-label data with EC filtered data...")
        EC_filtered = join_off_label_to_matrix(off_label_edges, EC_filtered)
        logging.info(f"EC filtered with off-label: {EC_filtered.shape}")

        logging.info("Joining off-label data with PSU filtered data...")
        PSU_filtered = join_off_label_to_matrix(off_label_edges, PSU_filtered)
        logging.info(f"PSU filtered with off-label: {PSU_filtered.shape}")

        logging.info("Preprocessing completed successfully!")
        return EC_filtered, PSU_filtered


def pr_auc_score(y_true, y_score):
    """Calculate Precision-Recall AUC."""
    if not isinstance(y_true, np.ndarray): 
        y_true = np.array(y_true)
    if not isinstance(y_score, np.ndarray): 
        y_score = np.array(y_score)
    return average_precision_score(y_true, y_score)


def calculate_metrics(
        matrix : pl.DataFrame,
        bool_test_col : str = "is_known_positive",
        score_col : str = "treat score",
        n_lst: list[int] = [20000, 40000, 60000, 80000, 100000],
        k_max : int = 100,
        threshold : float = 0.5,
        perform_sort : bool = True,
        out_of_matrix_mode : bool = False,
    ) -> None:

    results = {}

    # Classification metrics (uses threshold)
    y_pred_proba = matrix.filter(pl.col("is_known_positive") | pl.col("is_known_negative")).select("treat score").to_numpy().flatten()
    y_true = matrix.filter(
        pl.col("is_known_positive") | pl.col("is_known_negative")
    ).with_columns(
        pl.when(pl.col("is_known_positive"))
        .then(1)
        .when(pl.col("is_known_negative"))
        .then(0)
        .otherwise(None)
        .alias("label")
    ).select("label").to_numpy().flatten()
    
    y_pred_binary = (y_pred_proba >= threshold).astype(int)
    results["accuracy"] = np.mean(y_true == y_pred_binary)
    results["precision"] = precision_score(y_true, y_pred_binary, zero_division=0)
    results["recall"] = recall_score(y_true, y_pred_binary, zero_division=0)
    results["f1"] = f1_score(y_true, y_pred_binary, zero_division=0)
    
    # Threshold-free metrics
    results["pr_auc"] = pr_auc_score(y_true, y_pred_proba)


    # Calculate recall@n vs n
    recall_lst = give_recall_at_n(matrix=matrix, n_lst=n_lst, bool_test_col=bool_test_col, score_col=score_col, perform_sort=perform_sort, out_of_matrix_mode=out_of_matrix_mode)
    for n, recall in zip(n_lst, recall_lst):
        results[f"recall@{n}"] = recall

    # Hit@K
    hit_at_k = give_hit_at_k(matrix=matrix, k_max=k_max, bool_test_col=bool_test_col, score_col=score_col)
    for i in [1, 5, 10, 20, 100]:
        # Find the largest k value that is <= i
        valid_rows = hit_at_k.filter(pl.col("k") <= i)
        if len(valid_rows) > 0:
            # Get the row with the maximum k value among valid rows
            max_k_row = valid_rows.filter(pl.col("k") == valid_rows["k"].max())
            results[f"hit@{i}"] = max_k_row["hit_at_k"].item()
        else:
            # No valid k found, set to 0
            results[f"hit@{i}"] = 0.0
            
    # MRR
    results["mrr"] = give_disease_specific_mrr(matrix=matrix, bool_test_col=bool_test_col, score_col=score_col)

    return results


def create_styled_results_table(results_df):
    """
    Create a styled DataFrame for displaying model comparison results with highlighting for maximum values.

    Args:
        results_df (pd.DataFrame): DataFrame with models as rows and metrics as columns

    Returns:
        Styled DataFrame ready for display in Jupyter notebook
    """
    def highlight_max(s):
        """
        Highlight the maximum value in each column with a green background.
        """
        is_max = s == s.max()
        return ['background-color: #90EE90; font-weight: bold' if v else '' for v in is_max]

    # Format all numerical values to 4 decimal places and apply styling
    styled_df = results_df.style.format('{:.4f}') \
        .apply(highlight_max, axis=0) \
        .set_table_attributes('style="border-collapse: collapse; margin: 25px 0; font-size: 0.9em; font-family: sans-serif; min-width: 400px; border-radius: 5px; overflow: hidden; box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);"') \
        .set_table_styles([
            {'selector': 'thead th', 'props': [('background-color', '#009879'), ('color', 'white'), ('font-weight', 'bold'), ('text-align', 'center'), ('padding', '12px 15px')]},
            {'selector': 'tbody td', 'props': [('padding', '12px 15px'), ('text-align', 'center')]},
            {'selector': 'tbody tr:nth-of-type(even)', 'props': [('background-color', '#f3f3f3')]},
            {'selector': 'tbody tr:last-of-type', 'props': [('border-bottom', '2px solid #009879')]},
            {'selector': 'th', 'props': [('border', '1px solid #dddddd')]},
            {'selector': 'td', 'props': [('border', '1px solid #dddddd')]}
        ])

    return styled_df


def display_model_comparison_table(model_names, model_predictions, title="MODEL PERFORMANCE COMPARISON"):
    """
    Calculate metrics for multiple models and display them in a formatted comparison table.

    Args:
        model_names (list): List of model names for display
        model_predictions (list): List of model prediction matrices (polars DataFrames)
        title (str): Title to display above the table
    """
    from IPython.display import display

    # Define the metrics we want to display
    metric_columns = [
        'Accuracy', 'Precision', 'Recall', 'F1', 'PR-AUC', 'MRR',
        'Hit@1', 'Hit@5', 'Hit@10', 'Hit@20', 'Hit@100',
        'Recall@20000', 'Recall@40000', 'Recall@60000', 'Recall@80000', 'Recall@100000'
    ]

    # Create a list to store results for each model
    results_data = []

    for model_name, model_prediction in zip(model_names, model_predictions):
        results = calculate_metrics(model_prediction)

        # Extract metrics in the order defined above
        model_results = [
            results['accuracy'],
            results['precision'],
            results['recall'],
            results['f1'],
            results['pr_auc'],
            results['mrr'],
            results['hit@1'],
            results['hit@5'],
            results['hit@10'],
            results['hit@20'],
            results['hit@100'],
            results['recall@20000'],
            results['recall@40000'],
            results['recall@60000'],
            results['recall@80000'],
            results['recall@100000']
        ]

        results_data.append(model_results)

    # Create DataFrame with models as rows and metrics as columns
    results_df = pd.DataFrame(
        results_data,
        index=model_names,
        columns=metric_columns
    )

    # Format all numerical values to 4 decimal places
    results_df = results_df.round(4)

    # Display the table with enhanced formatting
    print("\n" + "="*120)
    print(title)
    print("="*120)

    # Create and display the styled table
    styled_df = create_styled_results_table(results_df)
    display(styled_df)


def create_disease_distribution_analysis(raw_data_split, matrix_predictions_dict):
    """
    Create a figure showing disease distribution analysis.

    This function analyzes the distribution of unique diseases across different
    disease types, data splits (TRAIN/TEST), and labels (positive/negative).

    Args:
        raw_data_split (pl.DataFrame): Raw data split with columns:
            - target: disease identifiers
            - y: binary labels (1 = positive, 0 = negative)
            - split: data partition ("TRAIN" or "TEST")
            - fold: disease type identifier (0-5)
        matrix_predictions_dict (dict): Mapping from disease type to matrix predictions

    Returns:
        matplotlib.figure.Figure: The created figure
    """

    # Define color scheme for disease types (consistent across all subplots)
    DISEASE_COLORS = {
        'Anemia': '#FF6B6B',                    # Red
        'Autoimmune Disease': '#4ECDC4',        # Teal
        'Cancer or Benign Tumor': '#45B7D1',    # Blue
        'Cardiovascular Disorder': '#96CEB4',     # Green
        'Metabolic Disease': '#FFEAA7',         # Yellow
        'Neurodegenerative Disease': '#DDA0DD'   # Plum
    }

    # Fold to disease type mapping
    FOLD_TO_DISEASE = {
        0: 'Anemia',
        1: 'Autoimmune Disease',
        2: 'Cancer or Benign Tumor',
        3: 'Cardiovascular Disorder',
        4: 'Metabolic Disease',
        5: 'Neurodegenerative Disease'
    }

    # Add disease type based on fold
    df = raw_data_split.with_columns(
        pl.col('fold').map_elements(
            lambda x: FOLD_TO_DISEASE.get(x, f'unknown_fold_{x}'),
            return_dtype=pl.Utf8
        ).alias('disease_type')
    )

    # Analyze disease distribution
    results = {}
    conditions = [
        ('TRAIN', 1, 'TRAIN - Positive'),
        ('TRAIN', 0, 'TRAIN - Negative'),
        ('TEST', 1, 'TEST - Positive'),
        ('TEST', 0, 'TEST - Negative')
    ]

    for split_val, label_val, condition_name in conditions:
        
        if split_val == 'TRAIN':
            # Filter data for this condition
            filtered_df = df.filter(
                (pl.col('split') == split_val) & (pl.col('y') == label_val)
            )

            # Count unique diseases per disease type
            disease_counts = (
                filtered_df
                .group_by('disease_type')
                .agg(pl.col('target').n_unique().alias('unique_disease_count'))
                .sort('disease_type')
            )

            # Convert to dictionary for easier handling
            disease_counts_dict = dict(zip(
                disease_counts['disease_type'].to_list(),
                disease_counts['unique_disease_count'].to_list()
            ))

            # Ensure all disease types are represented (fill missing with 0)
            for disease_type in FOLD_TO_DISEASE.values():
                if disease_type not in disease_counts_dict:
                    disease_counts_dict[disease_type] = 0
        else:
            disease_counts_dict = {}
            if label_val == 1:
                selected_column_name = "is_known_positive"
            else:
                selected_column_name = "is_known_negative"
            for disease_type in FOLD_TO_DISEASE.values():
                disease_counts_dict[disease_type] =len(matrix_predictions_dict[disease_type].filter(pl.col(selected_column_name))['target'].unique())
        
        results[condition_name] = disease_counts_dict


    # Set up the figure and subplots
    fig, axes = plt.subplots(2, 2, figsize=(16, 8))

    # Define subplot arrangement
    subplot_config = [
        (0, 0, 'TRAIN - Positive', 'Number of unique positive diseases (TRAIN)'),
        (0, 1, 'TRAIN - Negative', 'Number of unique negative diseases (TRAIN)'),
        (1, 0, 'TEST - Positive', 'Number of unique positive diseases (TEST)'),
        (1, 1, 'TEST - Negative', 'Number of unique negative diseases (TEST)')
    ]

    # Get all disease types in consistent order
    disease_types = list(FOLD_TO_DISEASE.values())

    for row, col, condition_key, title in subplot_config:
        ax = axes[row, col]
        data = results[condition_key]

        # Sort disease types by count in ascending order for this specific subplot
        disease_count_pairs = [(disease_type, data.get(disease_type, 0)) for disease_type in disease_types]
        disease_count_pairs.sort(key=lambda x: x[1], reverse=False)  # Sort by count, ascending

        # Extract sorted disease types and their corresponding counts and colors
        sorted_disease_types = [pair[0] for pair in disease_count_pairs]
        sorted_counts = [pair[1] for pair in disease_count_pairs]
        sorted_colors = [DISEASE_COLORS[disease_type] for disease_type in sorted_disease_types]

        # Create horizontal bar chart with sorted data
        y_pos = np.arange(len(sorted_disease_types))
        bars = ax.barh(y_pos, sorted_counts, color=sorted_colors, alpha=0.8, edgecolor='white', linewidth=0.5)

        # Customize the subplot
        ax.set_title(title, fontsize=12, fontweight='bold', pad=15)
        ax.set_xlabel('Number of diseases', fontsize=10)
        ax.set_yticks(y_pos)
        ax.set_yticklabels(sorted_disease_types, fontsize=8)

        # Set x-axis limits first
        if condition_key == 'TRAIN - Positive':
            ax.set_xlim(0, 2500)
            xlim_max = 2500
        elif condition_key == 'TRAIN - Negative':
            ax.set_xlim(0, 1200)
            xlim_max = 1500
        elif condition_key == 'TEST - Positive':
            ax.set_xlim(0, 500)
            xlim_max = 500
        else:  # TEST - Negative
            ax.set_xlim(0, 200)
            xlim_max = 200

        # Add value labels on bars using subplot-specific offset
        for bar, count in zip(bars, sorted_counts):
            if count > 0:
                ax.text(bar.get_width() + xlim_max * 0.01,
                       bar.get_y() + bar.get_height()/2,
                       str(count),
                       ha='left', va='center', fontsize=10, fontweight='bold')

        # Improve readability
        ax.grid(axis='x', alpha=0.3, linestyle='--')
        ax.tick_params(axis='y', labelsize=9)
        ax.tick_params(axis='x', labelsize=9)

        # Add subtle background
        ax.set_facecolor('#f8f9fa')

    # Adjust layout to prevent overlap
    plt.tight_layout()

    # Show the plot
    plt.show()

    return fig


def plot_treat_score_distributions(data1, data2=None, data1_name="Dataset 1", data2_name="Dataset 2",
                                  title="Treat Score Distributions", figsize=(16, 6), bins=100, alpha=0.7):
    """
    Plot the distributions of "treat score" column by categories "Known Positives" and "Known Negatives".
    Can plot one or two datasets side-by-side for comparison.

    Args:
        data1 (pl.DataFrame): First input table with columns:
            - 'treat score': numerical scores to plot
            - 'is_known_positive': boolean column indicating known positives
            - 'is_known_negative': boolean column indicating known negatives
        data2 (pl.DataFrame, optional): Second input table with same columns as data1
        data1_name (str): Name/label for the first dataset
        data2_name (str): Name/label for the second dataset
        title (str): Overall title for the plot
        figsize (tuple): Figure size as (width, height)
        bins (int): Number of bins for the histogram
        alpha (float): Transparency level for the histograms

    Returns:
        matplotlib.figure.Figure: The created figure
    """

    def extract_data(data):
        """Helper function to extract treat scores from Polars DataFrame"""
        if hasattr(data, 'filter'):  # Polars DataFrame
            known_positives = data.filter(pl.col('is_known_positive'))['treat score'].to_numpy()
            known_negatives = data.filter(pl.col('is_known_negative'))['treat score'].to_numpy()
        else:  # Pandas DataFrame
            known_positives = data[data['is_known_positive']]['treat score'].values
            known_negatives = data[data['is_known_negative']]['treat score'].values
        return known_positives, known_negatives

    def plot_single_dataset(ax, data, dataset_name, bins, alpha):
        """Helper function to plot a single dataset on given axis"""
        known_positives, known_negatives = extract_data(data)

        # Plot histograms with different colors
        ax.hist(known_positives, bins=bins, alpha=alpha, color='#2E8B57',
                label=f'Known Positives (n={len(known_positives):,})')
        ax.hist(known_negatives, bins=bins, alpha=alpha, color='#DC143C',
                label=f'Known Negatives (n={len(known_negatives):,})')

        # Customize the subplot
        ax.set_xlabel('Treat Score', fontsize=12)
        ax.set_ylabel('Count', fontsize=12)
        ax.set_title(dataset_name, fontsize=14, fontweight='bold', pad=20)
        ax.legend(fontsize=11)
        ax.grid(True, alpha=0.3, linestyle='--')
        ax.set_xlim(0.0, 1.0)

        # Add statistics text box with min/max values
        pos_min = np.min(known_positives)
        pos_max = np.max(known_positives)
        neg_min = np.min(known_negatives)
        neg_max = np.max(known_negatives)

        stats_text = f'Known Positives: min={pos_min:.4f}, max={pos_max:.4f}\n'
        stats_text += f'Known Negatives: min={neg_min:.4f}, max={neg_max:.4f}'

        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        return known_positives, known_negatives

    # Determine if we're plotting one or two datasets
    if data2 is None:
        # Single dataset plot
        fig, ax = plt.subplots(figsize=figsize)
        plot_single_dataset(ax, data1, data1_name, bins, alpha)
        fig.suptitle(title, fontsize=16, fontweight='bold', y=1.02)
    else:
        # Side-by-side comparison plot
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=figsize)

        # Plot both datasets
        data1_pos, data1_neg = plot_single_dataset(ax1, data1, data1_name, bins, alpha)
        data2_pos, data2_neg = plot_single_dataset(ax2, data2, data2_name, bins, alpha)

        # Set consistent y-axis limits for comparison
        all_counts_1, _ = np.histogram(np.concatenate([data1_pos, data1_neg]), bins=bins, range=(0.0, 1.0))
        all_counts_2, _ = np.histogram(np.concatenate([data2_pos, data2_neg]), bins=bins, range=(0.0, 1.0))
        max_count = max(np.max(all_counts_1), np.max(all_counts_2))

        ax1.set_ylim(0, max_count * 1.1)
        ax2.set_ylim(0, max_count * 1.1)

        # Add overall title
        fig.suptitle(title, fontsize=16, fontweight='bold', y=1.02)

    # Improve layout
    plt.tight_layout()

    # Show the plot
    plt.show()

    return fig

