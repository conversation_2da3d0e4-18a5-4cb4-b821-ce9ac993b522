"""
Generates new predictions for matrix_predictions.parquet using a specific base model.

This script:
1. Loads a specified base model from either the default models directory or a custom path
2. Precomputes expensive enhanced embedding operations once for efficiency
3. Processes the matrix_predictions.parquet file in batches to reduce memory usage
4. Generates new predictions to replace the 'treat score' column
5. Drops specified columns and saves the updated matrix

Usage:
    # Using predefined model names
    python evaluate_base_matrix_prediction.py --model_name xgb_clf --output_path updated_matrix.parquet
    python evaluate_base_matrix_prediction.py --model_name lgbm_clf --output_path updated_matrix.parquet
    python evaluate_base_matrix_prediction.py --model_name catboost_clf --output_path updated_matrix.parquet
    python evaluate_base_matrix_prediction.py --model_name tabnet_clf --output_path updated_matrix.parquet
    
    # Using custom model paths with batch processing
    python evaluate_base_matrix_prediction.py --model_path /path/to/custom_model.joblib --output_path updated_matrix.parquet --batch_size 5000
"""

import os
import sys
import numpy as np
import polars as pl
import argparse
from joblib import load as joblib_load

# Add the src directory to path to import modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from utils import (get_enhanced_embeddings_cached, combine_embeddings, setup_logger, get_timestamp,
                   check_similarity_matrices_exist, load_embeddings_and_degrees,
                   precompute_drug_profiles_from_edges, precompute_disease_profiles_from_edges,
                   precompute_drug_similarity_rows_to_disk, precompute_disease_similarity_rows_to_disk)
from config import MODELS_DIR, DATA_DIR, DEFAULT_FEATURE_COL, SEED, LOGS_DIR
import pickle
from tqdm import tqdm

# Setup logging
timestamp = get_timestamp()
os.makedirs(LOGS_DIR, exist_ok=True)
log_file = os.path.join(LOGS_DIR, f"evaluate_base_matrix_{timestamp}.log")
logger = setup_logger("evaluate_base_matrix", log_file)

# Setup paths
BASE_MODELS_DIR = os.path.join(MODELS_DIR, "base_models")
MATRIX_PREDICTIONS_PATH = os.path.join(DATA_DIR, "raw_data", "matrix_predictions.parquet")

np.random.seed(SEED)

# Columns to drop from the matrix
COLUMNS_TO_DROP = [
    'trial_sig_better', 'trial_non_sig_better', 'trial_sig_worse', 'trial_non_sig_worse',
    'not treat score', 'unknown score', 'rank', 'quantile_rank', '__index_level_0__'
]

def load_model(model_name=None, model_path=None):
    """Load a base model from predefined models or custom path."""
    if model_path:
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Custom model not found: {model_path}")
        logger.info(f"Loading custom model from: {model_path}")
        return joblib_load(model_path)
    
    if model_name:
        predefined_models = ['xgb_clf', 'lgbm_clf', 'catboost_clf', 'tabnet_clf']
        
        if model_name not in predefined_models:
            raise ValueError(f"Unknown model name: {model_name}. Available: {predefined_models}")
        
        model_file = f"{model_name}.joblib"
        model_path = os.path.join(BASE_MODELS_DIR, model_file)
        
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model not found: {model_path}")
        
        logger.info(f"Loading {model_name} model from: {model_path}")
        return joblib_load(model_path)
    
    raise ValueError("Either model_name or model_path must be specified")

def load_auxiliary_data():
    """Load auxiliary data needed for enhanced embeddings."""
    logger.info("Loading auxiliary data for enhanced embeddings...")
    
    # Load node embedding dictionary
    node_embedding_path = os.path.join(DATA_DIR, "processed_data", "node_embedding_dict.pkl")
    with open(node_embedding_path, "rb") as f:
        node_embedding_dict = pickle.load(f)
    logger.info(f"Loaded {len(node_embedding_dict)} node embeddings")
    
    # Load bidirectional knowledge graph edges
    bi_kg_edge_path = os.path.join(DATA_DIR, "cached", "bi_kg_edge.parquet")
    if not os.path.exists(bi_kg_edge_path):
        raise FileNotFoundError(f"bi_kg_edge.parquet not found at {bi_kg_edge_path}")
    bi_kg_edge = pl.read_parquet(bi_kg_edge_path)
    logger.info(f"Loaded bidirectional KG edges: {bi_kg_edge.shape}")
    
    # Load idx_map
    idx_map_path = os.path.join(DATA_DIR, "cached", "idx_map.pkl")
    if not os.path.exists(idx_map_path):
        raise FileNotFoundError(f"idx_map.pkl not found at {idx_map_path}")
    with open(idx_map_path, "rb") as f:
        idx_map = pickle.load(f)
    logger.info(f"Loaded idx_map with {sum(len(v) for v in idx_map.values())} total nodes")
    
    return node_embedding_dict, bi_kg_edge, idx_map

def precompute_enhanced_embedding_data(bi_kg_edge, idx_map, node_embedding_dict):
    """
    Precompute all expensive operations needed for enhanced embeddings.
    This should be called once before processing batches.
    
    Returns:
        dict: Dictionary containing all precomputed data needed for enhanced embeddings
    """
    logger.info("Precomputing expensive operations for enhanced embeddings...")
    logger.info("This will take several minutes but only needs to be done once...")
    
    # Step 1: Check if similarity matrices already exist on disk
    drug_sim_exists, disease_sim_exists, drug_id_to_idx, disease_id_to_idx = check_similarity_matrices_exist()

    if drug_sim_exists and disease_sim_exists:
        logger.info("Both drug and disease similarity matrices exist on disk. Loading embeddings and degrees only...")

        # Load only embeddings and degrees dictionaries (skip profiles)
        (drug_embeddings_dict, disease_embeddings_dict,
         drug_degrees_dict, disease_degrees_dict) = load_embeddings_and_degrees()

        # Check if all required dictionaries were loaded successfully
        if (drug_embeddings_dict is not None and disease_embeddings_dict is not None and
            drug_degrees_dict is not None and disease_degrees_dict is not None):
            logger.info("Successfully loaded all required dictionaries from cache. Skipping profile computation.")
        else:
            logger.warning("Some dictionaries failed to load. Falling back to full computation...")
            drug_sim_exists = disease_sim_exists = False

    if not (drug_sim_exists and disease_sim_exists):
        # Step 2: Pre-compute node profiles (with caching) - This is the expensive part!
        logger.info("Step 1/4: Precomputing drug profiles...")
        (drug_profiles_dict, drug_embeddings_dict, drug_degrees_dict) = precompute_drug_profiles_from_edges(
            bi_kg_edge, idx_map, node_embedding_dict
        )

        logger.info("Step 2/4: Precomputing disease profiles...")
        (disease_profiles_dict, disease_embeddings_dict, disease_degrees_dict) = precompute_disease_profiles_from_edges(
            bi_kg_edge, idx_map, node_embedding_dict
        )

        # Step 3: Pre-compute similarity rows to disk (memory-efficient)
        logger.info("Step 3/4: Precomputing drug similarity row mappings...")
        drug_id_to_idx = precompute_drug_similarity_rows_to_disk(drug_profiles_dict)

        logger.info("Step 4/4: Precomputing disease similarity row mappings...")
        disease_id_to_idx = precompute_disease_similarity_rows_to_disk(disease_profiles_dict)
    
    # Create reverse mappings for efficiency
    drug_idx_to_id = {idx: drug_id for drug_id, idx in drug_id_to_idx.items()}
    disease_idx_to_id = {idx: disease_id for disease_id, idx in disease_id_to_idx.items()}
    
    logger.info("Precomputation completed successfully!")
    logger.info(f"Precomputed data for {len(drug_embeddings_dict)} drugs and {len(disease_embeddings_dict)} diseases")
    
    return {
        'drug_embeddings_dict': drug_embeddings_dict,
        'disease_embeddings_dict': disease_embeddings_dict,
        'drug_degrees_dict': drug_degrees_dict,
        'disease_degrees_dict': disease_degrees_dict,
        'drug_id_to_idx': drug_id_to_idx,
        'disease_id_to_idx': disease_id_to_idx,
        'drug_idx_to_id': drug_idx_to_id,
        'disease_idx_to_id': disease_idx_to_id
    }

def generate_enhanced_embeddings_from_precomputed(pairs_df, precomputed_data, k=5, exp_lambda=0.7):
    """
    Generate enhanced embeddings using precomputed data.
    This is much faster since all expensive operations are already done.
    
    Args:
        pairs_df (pl.DataFrame): DataFrame with 'drug_id' and 'dis_id' columns
        precomputed_data (dict): Dictionary containing precomputed data from precompute_enhanced_embedding_data
        k (int): Number of top similar nodes to use for enhanced embeddings
        exp_lambda (float): Lambda parameter for exponential rarity weighting
    
    Returns:
        tuple: (enhanced_embeddings_list, valid_indices)
    """
    # Extract precomputed data
    drug_embeddings_dict = precomputed_data['drug_embeddings_dict']
    disease_embeddings_dict = precomputed_data['disease_embeddings_dict']
    drug_degrees_dict = precomputed_data['drug_degrees_dict']
    disease_degrees_dict = precomputed_data['disease_degrees_dict']
    drug_id_to_idx = precomputed_data['drug_id_to_idx']
    disease_id_to_idx = precomputed_data['disease_id_to_idx']
    drug_idx_to_id = precomputed_data['drug_idx_to_id']
    disease_idx_to_id = precomputed_data['disease_idx_to_id']
    
    # Find unique drugs and diseases to optimize computation
    unique_drugs = set(pairs_df['drug_id'].unique().to_list())
    unique_diseases = set(pairs_df['dis_id'].unique().to_list())
    
    logger.info(f"Found {len(unique_drugs)} unique drugs and {len(unique_diseases)} unique diseases in {pairs_df.shape[0]} pairs")
    logger.info(f"Optimization ratio: {pairs_df.shape[0]/(len(unique_drugs) + len(unique_diseases)):.1f}x fewer computations needed")
    
    # Pre-compute enhanced embeddings for unique nodes only
    enhanced_drug_cache = {}
    for drug_id in tqdm(unique_drugs, desc="Computing drug enhanced embeddings"):
        if drug_id in drug_embeddings_dict and drug_id in drug_id_to_idx:
            enhanced_emb = get_enhanced_embeddings_cached(
                drug_id, 'drug', drug_embeddings_dict, drug_degrees_dict, 
                drug_id_to_idx, drug_idx_to_id, k, exp_lambda
            )
            if enhanced_emb is not None:
                enhanced_drug_cache[drug_id] = enhanced_emb
    
    enhanced_disease_cache = {}
    for disease_id in tqdm(unique_diseases, desc="Computing disease enhanced embeddings"):
        if disease_id in disease_embeddings_dict and disease_id in disease_id_to_idx:
            enhanced_emb = get_enhanced_embeddings_cached(
                disease_id, 'disease', disease_embeddings_dict, disease_degrees_dict,
                disease_id_to_idx, disease_idx_to_id, k, exp_lambda
            )
            if enhanced_emb is not None:
                enhanced_disease_cache[disease_id] = enhanced_emb
    
    # Fast O(1) lookup for each pair
    enhanced_embeddings = []
    valid_indices = []
    
    for idx, row in enumerate(tqdm(pairs_df.iter_rows(named=True), total=pairs_df.shape[0], desc="Combining cached embeddings")):
        drug_id = row['drug_id']
        dis_id = row['dis_id']
        
        # O(1) lookup instead of O(K) computation
        enhanced_drug_emb = enhanced_drug_cache.get(drug_id)
        enhanced_dis_emb = enhanced_disease_cache.get(dis_id)
        
        if enhanced_drug_emb is not None and enhanced_dis_emb is not None:
            # Combine embeddings
            combined_emb = combine_embeddings(enhanced_drug_emb, enhanced_dis_emb)
            enhanced_embeddings.append(list(combined_emb))
            valid_indices.append(idx)
    
    return enhanced_embeddings, valid_indices

def generate_features_for_batch(batch_df, precomputed_data, k=5, exp_lambda=0.7):
    """
    Generate ENHANCED features for a batch of drug-disease pairs using precomputed data.
    
    Args:
        batch_df (pl.DataFrame): Batch of matrix predictions dataframe
        precomputed_data (dict): Precomputed data from precompute_enhanced_embedding_data
        k (int): Number of top similar nodes to use for enhanced embeddings
        exp_lambda (float): Lambda parameter for exponential rarity weighting
    
    Returns:
        pl.DataFrame: Batch with added enhanced feature column (only valid rows)
    """
    # Standardize column names for enhanced embedding function
    pairs_df = batch_df.select([
        pl.col('source').alias('drug_id'),
        pl.col('target').alias('dis_id')
    ])
    
    # Generate enhanced embeddings using precomputed data (much faster!)
    enhanced_embeddings, valid_indices = generate_enhanced_embeddings_from_precomputed(
        pairs_df, precomputed_data, k=k, exp_lambda=exp_lambda
    )
    
    # Filter batch to keep only rows with valid enhanced embeddings
    if len(valid_indices) < batch_df.shape[0]:
        batch_df = batch_df.with_row_index().filter(pl.col("index").is_in(valid_indices)).drop("index")
    
    # Add enhanced features to dataframe
    batch_df = batch_df.with_columns(pl.Series(name=DEFAULT_FEATURE_COL, values=enhanced_embeddings))
    
    return batch_df

def process_matrix_in_batches(matrix_path, model, precomputed_data, 
                             output_path, batch_size=10000, k=5, exp_lambda=0.7):
    """
    Process the matrix in batches to reduce memory consumption using precomputed data.
    
    Args:
        matrix_path (str): Path to matrix_predictions.parquet
        model: Trained model for predictions
        precomputed_data (dict): Precomputed data from precompute_enhanced_embedding_data
        output_path (str): Output path for updated matrix
        batch_size (int): Number of rows to process in each batch
        k (int): Number of top similar nodes for enhanced embeddings
        exp_lambda (float): Lambda parameter for exponential rarity weighting
    """
    logger.info(f"Processing matrix in batches of {batch_size} rows...")
    
    # Get total number of rows first
    total_rows = pl.scan_parquet(matrix_path).select(pl.len()).collect().item()
    logger.info(f"Total rows to process: {total_rows}")
    
    processed_batches = []
    valid_pairs_count = 0
    
    # Process in batches
    for batch_start in range(0, total_rows, batch_size):
        batch_end = min(batch_start + batch_size, total_rows)
        batch_num = batch_start // batch_size + 1
        total_batches = (total_rows + batch_size - 1) // batch_size
        
        logger.info(f"\nProcessing batch {batch_num}/{total_batches} (rows {batch_start}-{batch_end-1})...")
        
        # Load batch
        batch_df = pl.scan_parquet(matrix_path).slice(batch_start, batch_size).collect()
        logger.info(f"Loaded batch: {batch_df.shape}")
        
        # Generate enhanced features for this batch using precomputed data
        batch_with_features = generate_features_for_batch(
            batch_df, precomputed_data, k=k, exp_lambda=exp_lambda
        )
        
        if batch_with_features.shape[0] == 0:
            logger.warning(f"Batch {batch_num}: No valid enhanced embeddings generated, skipping...")
            continue
            
        logger.info(f"Batch {batch_num}: Generated enhanced features for {batch_with_features.shape[0]} pairs")
        valid_pairs_count += batch_with_features.shape[0]
        
        # Generate predictions for this batch
        X_features = np.array(batch_with_features[DEFAULT_FEATURE_COL].to_list())
        y_pred_proba = model.predict_proba(X_features)[:, 1]  # Get probability of positive class
        
        # Replace 'treat score' column with new predictions
        batch_with_predictions = batch_with_features.with_columns(
            pl.Series(name='treat score', values=y_pred_proba)
        )
        
        # Drop specified columns and the feature column
        columns_to_drop_final = [col for col in COLUMNS_TO_DROP + [DEFAULT_FEATURE_COL] 
                               if col in batch_with_predictions.columns]
        if columns_to_drop_final:
            final_batch = batch_with_predictions.drop(columns_to_drop_final)
        else:
            final_batch = batch_with_predictions.drop(DEFAULT_FEATURE_COL)
        
        processed_batches.append(final_batch)
        logger.info(f"Batch {batch_num}: Generated predictions for {final_batch.shape[0]} pairs")
    
    # Combine all processed batches
    if not processed_batches:
        raise ValueError("No valid batches were processed!")
    
    logger.info(f"\nCombining {len(processed_batches)} processed batches...")
    final_matrix = pl.concat(processed_batches)
    
    logger.info(f"Total valid pairs processed: {valid_pairs_count}")
    logger.info(f"Final matrix shape: {final_matrix.shape}")
    
    # Save updated matrix
    logger.info(f"Saving updated matrix to: {output_path}")
    os.makedirs(os.path.dirname(output_path) if os.path.dirname(output_path) else '.', exist_ok=True)
    final_matrix.write_parquet(output_path)
    
    return final_matrix

def main():
    """Main function to generate new predictions for matrix_predictions.parquet."""
    parser = argparse.ArgumentParser(description="Generate predictions for matrix using a base model with enhanced embeddings")
    
    # Model selection (mutually exclusive)
    model_group = parser.add_mutually_exclusive_group(required=True)
    model_group.add_argument('--model_name', choices=['xgb_clf', 'lgbm_clf', 'catboost_clf', 'tabnet_clf'], 
                            help='Name of predefined model to use')
    model_group.add_argument('--model_path', help='Path to custom model file')
    
    # Output and enhanced embedding parameters
    parser.add_argument('--output_path', required=True, help='Output path for updated matrix')
    parser.add_argument('--batch_size', type=int, default=10000, help='Batch size for processing (default: 10000)')
    parser.add_argument('--k', type=int, default=5, help='Number of top similar nodes for enhanced embeddings (default: 5)')
    parser.add_argument('--exp_lambda', type=float, default=0.7, help='Lambda parameter for exponential rarity weighting (default: 0.7)')
    
    args = parser.parse_args()
    
    try:
        # Load the model
        model = load_model(args.model_name, args.model_path)
        
        # Load auxiliary data for enhanced embeddings
        node_embedding_dict, bi_kg_edge, idx_map = load_auxiliary_data()
        
        # Check if matrix predictions file exists
        logger.info(f"Matrix predictions file: {MATRIX_PREDICTIONS_PATH}")
        if not os.path.exists(MATRIX_PREDICTIONS_PATH):
            raise FileNotFoundError(f"Matrix predictions file not found: {MATRIX_PREDICTIONS_PATH}")
        
        # Precompute expensive operations once
        logger.info("Using batch processing mode for memory efficiency...")
        precomputed_data = precompute_enhanced_embedding_data(bi_kg_edge, idx_map, node_embedding_dict)
        
        # Process matrix in batches
        final_matrix = process_matrix_in_batches(
            MATRIX_PREDICTIONS_PATH, model, precomputed_data,
            args.output_path, batch_size=args.batch_size, k=args.k, exp_lambda=args.exp_lambda
        )
        
        logger.info("Matrix evaluation completed successfully!")
        logger.info(f"Final matrix shape: {final_matrix.shape}")
        logger.info(f"Updated matrix saved to: {args.output_path}")
        
    except Exception as e:
        logger.error(f"Error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main()) 