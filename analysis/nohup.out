2025-07-23 15:46:11,018 - evaluate_meta_matrix - INFO - Loading default stacking model from: /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/analysis/src/../../models/multi_level_stacking_model.joblib
2025-07-23 15:46:45,473 - evaluate_meta_matrix - INFO - Loaded 4166283 node embeddings
2025-07-23 15:46:45,474 - evaluate_meta_matrix - INFO - Matrix predictions file: /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/analysis/src/../../data/raw_data/matrix_predictions.parquet
2025-07-23 15:46:45,474 - evaluate_meta_matrix - INFO - Initializing ensemble predictor with enhanced embeddings...
2025-07-23 15:46:45,475 - inference - INFO - EnsemblePredictor initialized (enhanced_embeddings=True, k=5, lambda=0.7)
2025-07-23 15:46:45,475 - evaluate_meta_matrix - INFO - Loading base models...
2025-07-23 15:46:45,475 - inference - INFO - Loading base models...
2025-07-23 15:46:46,285 - inference - INFO - Loaded xgb_clf from /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/analysis/src/../../models/base_models/xgb_clf.joblib
2025-07-23 15:46:47,222 - inference - INFO - Loaded lgbm_clf from /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/analysis/src/../../models/base_models/lgbm_clf.joblib
2025-07-23 15:46:47,502 - inference - INFO - Loaded catboost_clf from /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/analysis/src/../../models/base_models/catboost_clf.joblib
/home/<USER>/cqm5886/miniconda3/envs/exps2_ensemble_model_new_env/lib/python3.11/site-packages/torch/storage.py:414: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  return torch.load(io.BytesIO(b))
2025-07-23 15:46:53,055 - inference - INFO - Loaded tabnet_clf from /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/analysis/src/../../models/base_models/tabnet_clf.joblib
2025-07-23 15:46:53,055 - inference - INFO - All base models loaded successfully.
2025-07-23 15:46:53,055 - evaluate_meta_matrix - INFO - Precomputing enhanced embedding data for batch efficiency...
2025-07-23 15:46:53,056 - inference - INFO - Loading enhanced embedding data...
2025-07-23 15:46:54,677 - inference - INFO - Enhanced embedding data loaded successfully.
2025-07-23 15:46:54,678 - inference - INFO - Precomputing expensive operations for enhanced embeddings...
2025-07-23 15:46:54,678 - inference - INFO - This will take several minutes but only needs to be done once...
2025-07-23 15:46:54,903 - inference - INFO - Both drug and disease similarity matrices exist on disk. Loading embeddings and degrees only...
2025-07-23 15:46:58,750 - utils - INFO - Loaded drug_embeddings from cache: 656812 entries
2025-07-23 15:46:59,934 - utils - INFO - Loaded disease_embeddings from cache: 210553 entries
2025-07-23 15:47:00,061 - utils - INFO - Loaded drug_degrees from cache: 656812 entries
2025-07-23 15:47:00,137 - utils - INFO - Loaded disease_degrees from cache: 210553 entries
2025-07-23 15:47:00,137 - inference - INFO - Successfully loaded all required dictionaries from cache. Skipping profile computation.
2025-07-23 15:47:00,195 - inference - INFO - Precomputation completed successfully!
2025-07-23 15:47:00,195 - inference - INFO - Precomputed data for 656812 drugs and 210553 diseases
2025-07-23 15:47:00,195 - evaluate_meta_matrix - INFO - Using batch processing mode for memory efficiency...
2025-07-23 15:47:00,195 - evaluate_meta_matrix - INFO - Processing matrix in batches of 5000000 rows...
2025-07-23 15:47:00,208 - evaluate_meta_matrix - INFO - Total rows to process: 63148207

  0%|          | 0/13 [00:00<?, ?it/s]2025-07-23 15:47:00,212 - evaluate_meta_matrix - INFO - 
Processing batch 1/13 (rows 0-4999999)...
2025-07-23 15:47:00,344 - evaluate_meta_matrix - INFO - Loaded batch: (5000000, 15)
2025-07-23 15:47:00,369 - evaluate_meta_matrix - INFO - Batch 1: Generating ensemble predictions with enhanced embeddings...
2025-07-23 15:47:04,478 - inference - INFO - Creating enhanced embeddings using precomputed data for 5000000 pairs...
2025-07-23 15:47:04,947 - inference - INFO - Found 2779 unique drugs and 22074 unique diseases
2025-07-23 15:53:46,406 - inference - INFO - Successfully created enhanced embeddings for 4882416 pairs using precomputed data
2025-07-23 15:54:00,830 - inference - INFO - Generating base model predictions...
/home/<USER>/cqm5886/miniconda3/envs/exps2_ensemble_model_new_env/lib/python3.11/site-packages/xgboost/core.py:729: UserWarning: [15:54:00] WARNING: /workspace/src/common/error_msg.cc:58: Falling back to prediction using DMatrix due to mismatched devices. This might lead to higher memory usage and slower performance. XGBoost is running on: cuda:3, while the input data is on: cpu.
Potential solutions:
- Use a data structure that matches the device ordinal in the booster.
- Set the device for booster before call to inplace_predict.

This warning will only be shown once.

  return func(**kwargs)
/home/<USER>/cqm5886/miniconda3/envs/exps2_ensemble_model_new_env/lib/python3.11/site-packages/sklearn/utils/validation.py:2749: UserWarning: X does not have valid feature names, but LGBMClassifier was fitted with feature names
  warnings.warn(
