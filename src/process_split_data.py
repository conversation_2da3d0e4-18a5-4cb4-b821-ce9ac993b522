"""
Data splitting module for drug-disease repurposing ensemble model.

This script processes raw data splits and creates train/validation/test splits
based on diseases to ensure proper evaluation of drug-disease ranking models.

Key Functions:
1. Loads raw data split file with fold information
2. Splits training data into train and validation sets by disease
3. Ensures validation set has same number of diseases as test set
4. Maintains disease-based splitting to prevent data leakage
5. Saves processed data splits for downstream pipeline steps

"""

import os
import polars as pl
import random

from config import (
    DATA_DIR, N_SOFT_NEG_PER_POS, SOFT_NEGATIVE_WEIGHT, SEED, 
    EMBED_TYPE, LOGS_DIR, DEFAULT_FEATURE_COL
)
from utils import setup_logger, get_timestamp

# Set up logging
timestamp = get_timestamp()
os.makedirs(LOGS_DIR, exist_ok=True)
log_file = os.path.join(LOGS_DIR, f"process_split_data_{timestamp}.log")
logger = setup_logger("process_split_data", log_file)

# Set random seed for reproducibility
random.seed(SEED)


def split_train_to_train_and_valid(train_df: pl.DataFrame, test_df: pl.DataFrame):
    """
    Split the training data into train and validation sets based on diseases.
    The validation set will have the same number of unique diseases as the test set.
    
    Args:
        train_df (pl.DataFrame): Training data with 'target' (disease) column
        test_df (pl.DataFrame): Test data with 'target' (disease) column
    
    Returns:
        tuple: (train_df, valid_df) - filtered training and new validation dataframes
    """
    logger.info("Splitting training data into train and validation sets...")
    
    # Get the number of unique diseases in test set
    valid_disease_num = len(test_df['target'].unique().to_list())
    logger.info(f"Test set has {valid_disease_num} unique diseases")
    
    # Get all unique diseases from training set
    train_diseases = train_df['target'].unique().to_list()
    logger.info(f"Training set has {len(train_diseases)} unique diseases")
    
    # Randomly select diseases for validation set
    valid_diseases = random.sample(train_diseases, valid_disease_num)
    logger.info(f"Selected {len(valid_diseases)} diseases for validation set")
    
    # Create validation set from training data with selected diseases
    valid_df = train_df.filter(pl.col("target").is_in(valid_diseases))
    
    # Remove selected diseases from training set
    new_train_df = train_df.filter(~pl.col("target").is_in(valid_diseases))
    
    logger.info(f"Training set split: {new_train_df.shape[0]} samples remain in train, {valid_df.shape[0]} samples moved to validation")
    
    return new_train_df, valid_df


def process_data_split(fold_id: int = 0):
    """
    Process the raw data split by creating train/validation/test splits.
    
    Args:
        fold_id (int): Fold ID to process (default: 0)
    """
    logger.info(f"Processing data split for fold {fold_id}...")
    
    # Check if raw data split file exists
    raw_data_split_path = os.path.join(DATA_DIR, "raw_data", "raw_data_split.parquet")
    if not os.path.exists(raw_data_split_path):
        raise FileNotFoundError(f"Raw data split file not found: {raw_data_split_path}")
    
    logger.info(f"Loading raw data split from: {raw_data_split_path}")
    raw_data_split_df = pl.read_parquet(raw_data_split_path)
    
    # Filter the raw data split df to only include the specified fold_id
    raw_data_split_df = raw_data_split_df.filter(pl.col("fold") == fold_id)
    logger.info(f"Filtered to fold {fold_id}: {raw_data_split_df.shape[0]} samples")
    
    if raw_data_split_df.is_empty():
        raise ValueError(f"No data found for fold {fold_id}")
    
    # Extract data for TRAIN and TEST sets
    train_df = raw_data_split_df.filter(pl.col("split") == "TRAIN")
    test_df = raw_data_split_df.filter(pl.col("split") == "TEST")
    
    logger.info(f"Initial split sizes - Train: {train_df.shape[0]}, Test: {test_df.shape[0]}")
    
    if train_df.is_empty():
        raise ValueError("No training data found")
    if test_df.is_empty():
        raise ValueError("No test data found")
    
    # Split the train set into train and valid sets
    train_df, valid_df = split_train_to_train_and_valid(train_df, test_df)
    
    # Set the split label for validation data
    valid_df = valid_df.with_columns(pl.lit('VALID').alias('split'))
    
    # Combine all splits
    df = pl.concat([train_df, valid_df, test_df], how="vertical")
    logger.info(f"Final combined dataset shape: {df.shape}")
    
    # Log final split sizes
    for split_name in ['TRAIN', 'VALID', 'TEST']:
        split_size = df.filter(pl.col("split") == split_name).shape[0]
        logger.info(f"Final {split_name} set size: {split_size}")
    
    # Save the processed data split
    output_path = os.path.join(DATA_DIR, "raw_data", "processed_data_split.parquet")
    df.write_parquet(output_path)
    logger.info(f"Processed data split saved to: {output_path}")


def main():
    """
    Main function to process data split.
    """
    try:
        logger.info("Starting data split processing pipeline...")
        process_data_split(fold_id=0)
        logger.info("Data split processing completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in data split processing: {e}", exc_info=True)
        raise


if __name__ == "__main__":
    main()
