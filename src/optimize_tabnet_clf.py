"""
Hyperparameter optimization for TabNet Classifier using Bayesian optimization (scikit-optimize).

This script:
1. Loads the prepared training dataset
2. Defines search spaces for TabNet hyperparameters
3. Uses Bayesian optimization to find optimal hyperparameters
4. Saves the best parameters and optimization results

Usage:
    python optimize_tabnet_clf.py --n_calls 50
"""

import os
import sys
import numpy as np
import argparse
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import average_precision_score
from skopt import gp_minimize
from skopt.space import Real, Integer, Categorical
from skopt.utils import use_named_args

# Add src to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from hyperparameter_optimization_base import (
    load_prepared_data, save_optimization_results,
    setup_logger, get_timestamp
)
from config import SEED, LOGS_DIR

# Setup logging
timestamp = get_timestamp()
os.makedirs(LOGS_DIR, exist_ok=True)
log_file = os.path.join(LOGS_DIR, f"optimize_tabnet_clf_{timestamp}.log")
logger = setup_logger("tabnet_clf_opt", log_file)

# Import centralized TabNet utilities
from tabnet_utils import (
    get_tabnet_classifier,
    is_tabnet_available,
    prepare_tabnet_params,
    create_tabnet_model
)

# Define search space for TabNet hyperparameters
SEARCH_SPACE = [
    Integer(16, 128, name='n_d'),                    # Width of the decision prediction layer
    Integer(16, 128, name='n_a'),                    # Width of the attention embedding
    Integer(3, 10, name='n_steps'),                  # Number of steps in the architecture
    Real(1.0, 2.0, name='gamma'),                    # Coefficient for feature reusage
    Integer(1, 5, name='n_independent'),             # Number of independent GLU layers
    Integer(1, 5, name='n_shared'),                  # Number of shared GLU layers
    Real(0.01, 0.4, name='momentum'),                # Momentum for batch normalization
    Real(1e-5, 1e-2, name='lambda_sparse', prior='log-uniform'),  # Sparsity loss coefficient
    Categorical(['sparsemax', 'entmax'], name='mask_type'),  # Masking function
    # Training parameters (passed to fit method)
    Integer(50, 300, name='max_epochs'),             # Maximum epochs
    Integer(256, 1024, name='batch_size'),           # Batch size
    Integer(64, 512, name='virtual_batch_size'),     # Virtual batch size for GBN
    Integer(10, 30, name='patience'),                # Early stopping patience
]

def objective(X, y, weights, **params):
    """Objective function for TabNet Classifier optimization."""
    if not is_tabnet_available():
        logger.error("TabNet not available for optimization")
        return 0.0
    
    try:
        logger.info(f"Evaluating parameters: {params}")
        
        # Convert numpy types to native Python types for TabNet compatibility
        cleaned_params = {}
        for key, value in params.items():
            if isinstance(value, (np.integer, np.int64, np.int32)):
                cleaned_params[key] = int(value)
            elif isinstance(value, (np.floating, np.float64, np.float32)):
                cleaned_params[key] = float(value)
            elif isinstance(value, (np.str_, str)):
                cleaned_params[key] = str(value)
            else:
                cleaned_params[key] = value
        
        # Ensure virtual_batch_size <= batch_size
        if cleaned_params['virtual_batch_size'] > cleaned_params['batch_size']:
            cleaned_params['virtual_batch_size'] = cleaned_params['batch_size'] // 2
        
        cv = StratifiedKFold(n_splits=3, shuffle=True, random_state=SEED)
        cv_splits = list(cv.split(X, y))
        
        scores = []
        
        for train_idx, val_idx in cv_splits:
            X_train_fold, X_val_fold = X[train_idx], X[val_idx]
            y_train_fold, y_val_fold = y[train_idx], y[val_idx]
            w_train_fold, w_val_fold = weights[train_idx], weights[val_idx]
            
            # Separate constructor parameters from fit parameters
            constructor_params = {
                'n_d': cleaned_params['n_d'],
                'n_a': cleaned_params['n_a'],
                'n_steps': cleaned_params['n_steps'],
                'gamma': cleaned_params['gamma'],
                'n_independent': cleaned_params['n_independent'],
                'n_shared': cleaned_params['n_shared'],
                'momentum': cleaned_params['momentum'],
                'lambda_sparse': cleaned_params['lambda_sparse'],
                'mask_type': cleaned_params['mask_type'],
                'verbose': 0,  # Suppress output during optimization
                'seed': SEED
            }
            
            fit_params = {
                'max_epochs': cleaned_params['max_epochs'],
                'patience': cleaned_params['patience'],
                'batch_size': cleaned_params['batch_size'],
                'virtual_batch_size': cleaned_params['virtual_batch_size']
            }
            
            # Create TabNet model with constructor parameters only
            model, _ = create_tabnet_model(
                model_type='classifier',
                base_params=constructor_params,
                gpu_id=None  # Use default from config (GPU_DEVICE_ID_TABNET)
            )
            
            # Fit model with training parameters and sample weights
            model.fit(
                X_train_fold, y_train_fold,
                eval_set=[(X_val_fold, y_val_fold)],
                weights=w_train_fold,  # TabNet supports sample weights!
                eval_metric=['auc'],
                **fit_params
            )
            
            # Get predictions
            y_pred = model.predict_proba(X_val_fold)[:, 1]
            
            # Calculate weighted PR-AUC
            score = average_precision_score(y_val_fold, y_pred, sample_weight=w_val_fold)
            scores.append(score)
        
        # Return negative score for minimization
        avg_score = np.mean(scores)
        logger.info(f"CV PR-AUC: {avg_score:.4f}")
        return -avg_score
        
    except Exception as e:
        logger.warning(f"Error in objective function: {e}")
        return 0.0

def optimize_tabnet_classifier(n_calls=50):
    """Optimize hyperparameters for TabNet Classifier."""
    if not is_tabnet_available():
        raise RuntimeError("TabNet not available for optimization")
    
    logger.info("Starting hyperparameter optimization for TabNet Classifier...")
    logger.info(f"Search space: {SEARCH_SPACE}")
    logger.info(f"Number of optimization calls: {n_calls}")
    
    # Load data
    X, y, weights = load_prepared_data()
    logger.info(f"Loaded data: X.shape={X.shape}, y.shape={y.shape}")
    
    # Create objective function with @use_named_args decorator
    @use_named_args(SEARCH_SPACE)
    def objective_func(**params):
        return objective(X, y, weights, **params)
    
    # Perform Bayesian optimization
    result = gp_minimize(
        func=objective_func,
        dimensions=SEARCH_SPACE,
        n_calls=n_calls,
        n_initial_points=min(10, n_calls // 3),  # Adaptive initial points
        random_state=SEED,
        acq_func='EI',  # Expected Improvement
        n_jobs=1  # TabNet handles parallelization internally
    )
    
    # Extract best parameters
    best_params = {}
    for i, param_name in enumerate([dim.name for dim in SEARCH_SPACE]):
        best_params[param_name] = result.x[i]
    
    # Ensure virtual_batch_size constraint
    if best_params['virtual_batch_size'] > best_params['batch_size']:
        best_params['virtual_batch_size'] = best_params['batch_size'] // 2
    
    logger.info(f"Optimization completed for TabNet Classifier")
    logger.info(f"Best score: {-result.fun:.4f}")
    logger.info(f"Best parameters: {best_params}")
    
    optimization_result = {
        'model_name': 'tabnet_clf',
        'best_params': best_params,
        'best_score': -result.fun
    }
    
    # Save results
    save_optimization_results(optimization_result, 'tabnet_clf')
    
    return optimization_result

def main():
    """Main entry point for command line execution."""
    parser = argparse.ArgumentParser(description="Optimize TabNet Classifier hyperparameters")
    parser.add_argument("--n_calls", type=int, default=30, 
                       help="Number of optimization calls (default: 30)")
    args = parser.parse_args()
    
    logger.info(f"Starting TabNet Classifier optimization with {args.n_calls} calls...")
    
    try:
        # Run optimization with the specified n_calls
        result = optimize_tabnet_classifier(n_calls=args.n_calls)
        
        logger.info("TabNet Classifier optimization completed!")
        logger.info(f"Best score: {result['best_score']:.6f}")
        return True
        
    except Exception as e:
        logger.error(f"TabNet optimization failed: {e}")
        raise

if __name__ == "__main__":
    main() 