"""
Data preparation module for drug-disease repurposing ensemble model.

This script:
1. Loads drug/disease embeddings and pre-split labeled pairs.
2. Generates an initial set of "soft negative" pairs.
3. Combines all pairs (positive, hard negative, soft negative) with their concatenated embeddings 
   into a single feature matrix.
4. Saves the processed dataset (`full_dataset_with_features.parquet`).
5. Provides a function to synthesize additional soft negatives dynamically.

Usage:
    python prepare_dataset.py 
"""

import polars as pl
import numpy as np
import os
import pickle
import argparse
from tqdm import tqdm
from pathlib import Path

from config import (
    DATA_DIR, EMBED_TYPE, SOFT_NEGATIVE_WEIGHT, N_SOFT_NEG_PER_POS,
    DEFAULT_FEATURE_COL, SEED, VERBOSE, LOGS_DIR
)
from utils import (
    setup_logger, get_timestamp, preprocess_kg, 
    reverse_rel_generation, merge_node_edges, 
    create_enhanced_embeddings_batch_optimized
)

timestamp = get_timestamp()
Path(LOGS_DIR).mkdir(parents=True, exist_ok=True)
log_file = os.path.join(LOGS_DIR, f"prepare_dataset_{timestamp}.log")
logger = setup_logger("prepare_dataset", log_file)

# Set random seed for reproducibility
np.random.seed(SEED)

def clear_kg_cache():
    """
    Clear cached KG processing results.
    Useful when underlying KG data has changed and cache needs to be regenerated.
    """
    cache_dir = os.path.join(DATA_DIR, "cached")
    kg_cache_files = {
        'kg_edge': os.path.join(cache_dir, "kg_edge.parquet"),
        'bi_kg_edge': os.path.join(cache_dir, "bi_kg_edge.parquet"),
        'node_id_to_name': os.path.join(cache_dir, "node_id_to_name.pkl"),
        'node_id_to_category': os.path.join(cache_dir, "node_id_to_category.pkl"),
        'idx_map': os.path.join(cache_dir, "idx_map.pkl")
    }
    
    removed_count = 0
    for cache_name, cache_path in kg_cache_files.items():
        if os.path.exists(cache_path):
            try:
                os.remove(cache_path)
                removed_count += 1
                logger.info(f"Removed cached file: {cache_name}")
            except OSError as e:
                logger.warning(f"Failed to remove {cache_name}: {e}")
    
    if removed_count > 0:
        logger.info(f"Cleared {removed_count} KG cache files")
    else:
        logger.info("No KG cache files found to clear")

def load_raw_data():
    """
    Load embeddings, drug/disease lists, and pre-split labeled pairs.
    
    Returns:
        tuple: (drug_list_df, disease_list_df, processed_data_split_df, node_embedding_dict, 
                node_id_to_name, node_id_to_category, idx_map, bi_kg_edge)
    
    Raises:
        FileNotFoundError: If any required raw data files are missing.
    """
    try:
        logger.info("Loading raw data components...")
        
        # Pre-split labeled pairs (indications and contraindications)
        processed_data_split_path = os.path.join(DATA_DIR, "raw_data", "processed_data_split.parquet")
        if not os.path.exists(processed_data_split_path):
            raise FileNotFoundError(f"Processed data split file not found: {processed_data_split_path}")
        processed_data_split_df = pl.read_parquet(processed_data_split_path)
        # Standardize column names if necessary, ensure 'drug_id', 'dis_id', 'label', 'split'
        # Assuming: 'source' -> 'drug_id', 'target' -> 'dis_id', 'y' -> 'label'
        column_renames = {}
        if 'source' in processed_data_split_df.columns: 
            column_renames['source'] = 'drug_id'
        if 'target' in processed_data_split_df.columns: 
            column_renames['target'] = 'dis_id'
        if 'y' in processed_data_split_df.columns: 
            column_renames['y'] = 'label'
        if column_renames:
            processed_data_split_df = processed_data_split_df.rename(column_renames)
        
        # Ensure required columns exist
        required_cols = ['drug_id', 'dis_id', 'label', 'split']
        for col in required_cols:
            if col not in processed_data_split_df.columns:
                raise ValueError(f"Missing required column '{col}' in processed_data_split.parquet")
        logger.info(f"Loaded processed_data_split: {processed_data_split_df.shape}")

        node_embeddings_path = os.path.join(DATA_DIR, "raw_data", "nodes_with_embeddings.parquet")
        if not os.path.exists(node_embeddings_path):
            raise FileNotFoundError(f"Node embeddings file not found: {node_embeddings_path}")
        node_embeddings_df = pl.read_parquet(node_embeddings_path)

        if EMBED_TYPE not in node_embeddings_df.columns:
            raise ValueError(f"Specified EMBED_TYPE '{EMBED_TYPE}' not found in node_embeddings.parquet columns: {node_embeddings_df.columns}")

        # Create a dictionary for faster embedding lookup: {node_id: embedding_vector}
        node_embedding_dict = {
            row['id']: np.array(row[EMBED_TYPE]) 
            for row in node_embeddings_df.select(['id', EMBED_TYPE]).iter_rows(named=True)
            if row[EMBED_TYPE] is not None # Ensure embedding exists
        }
        logger.info(f"Loaded node_embeddings: {len(node_embedding_dict)} entities with embeddings of type '{EMBED_TYPE}'")

        # Check if KG processing results are cached
        cache_dir = os.path.join(DATA_DIR, "cached")
        kg_cache_files = {
            'kg_edge': os.path.join(cache_dir, "kg_edge.parquet"),
            'bi_kg_edge': os.path.join(cache_dir, "bi_kg_edge.parquet"),
            'node_id_to_name': os.path.join(cache_dir, "node_id_to_name.pkl"),
            'node_id_to_category': os.path.join(cache_dir, "node_id_to_category.pkl"),
            'idx_map': os.path.join(cache_dir, "idx_map.pkl")
        }
        
        # Check if all KG cache files exist
        kg_cached = all(os.path.exists(path) for path in kg_cache_files.values())
        
        if kg_cached:
            logger.info("Loading cached KG processing results...")
            try:
                # Load cached KG data
                kg_edge = pl.read_parquet(kg_cache_files['kg_edge'])
                bi_kg_edge = pl.read_parquet(kg_cache_files['bi_kg_edge'])
                
                with open(kg_cache_files['node_id_to_name'], 'rb') as f:
                    node_id_to_name = pickle.load(f)
                with open(kg_cache_files['node_id_to_category'], 'rb') as f:
                    node_id_to_category = pickle.load(f)
                with open(kg_cache_files['idx_map'], 'rb') as f:
                    idx_map = pickle.load(f)
                
                logger.info(f"Loaded cached KG data: {bi_kg_edge.shape[0]} bidirectional edges")
                
            except Exception as e:
                logger.warning(f"Failed to load cached KG data: {e}. Reprocessing...")
                kg_cached = False
        
        if not kg_cached:
            logger.info("Processing KG data from scratch...")
            
            # load kg data
            kg_raw_node = pl.read_parquet(os.path.join(DATA_DIR, "raw_data", "ec_kg", "nodes_c_filtered.parquet"))
            kg_raw_edge = pl.read_parquet(os.path.join(DATA_DIR, "raw_data", "ec_kg", "edges_c_filtered.parquet"))

            # merge node edges
            kg_raw_node = kg_raw_node.with_columns(pl.col('category').str.replace('biolink:', ''))
            kg_raw_node = merge_node_edges(kg_raw_node, ['Disease', 'PhenotypicFeature', 'DiseaseOrPhenotypicFeature'], 'Disease')
            kg_raw_node = merge_node_edges(kg_raw_node, ['SmallMolecule', 'Drug', 'ChemicalEntity', 'ComplexMolecularMixture', 'MolecularEntity', 'MolecularMixture'], 'Drug')

            # preprocess kg data
            kg_edge, node_id_to_name, node_id_to_category, idx_map = preprocess_kg(kg_raw_node, kg_raw_edge)

            # reverse rel generation
            bi_kg_edge = reverse_rel_generation(kg_edge)
            logger.info(f"Generated bidirectional KG edges: {bi_kg_edge.shape}")
            
            # Save processed KG data to cache
            os.makedirs(cache_dir, exist_ok=True)
            try:
                kg_edge.write_parquet(kg_cache_files['kg_edge'])
                bi_kg_edge.write_parquet(kg_cache_files['bi_kg_edge'])
                
                with open(kg_cache_files['node_id_to_name'], 'wb') as f:
                    pickle.dump(node_id_to_name, f)
                with open(kg_cache_files['node_id_to_category'], 'wb') as f:
                    pickle.dump(node_id_to_category, f)
                with open(kg_cache_files['idx_map'], 'wb') as f:
                    pickle.dump(idx_map, f)
                
                logger.info("Successfully cached KG processing results")
            except Exception as e:
                logger.warning(f"Failed to cache KG processing results: {e}")

        # Load drug and disease lists
        drug_list_path = os.path.join(DATA_DIR, "raw_data", "drug_list_norm.tsv")
        if not os.path.exists(drug_list_path):
            raise FileNotFoundError(f"Drug list file not found: {drug_list_path}")
        drug_list_df = pl.read_csv(drug_list_path, separator="\t")
        drug_list_df = drug_list_df.filter(pl.col('id') != "['Error']") # Filter out erroneous entries
        drug_list_df = drug_list_df.filter(pl.col('id').is_in(node_embedding_dict.keys()))
        drug_list_df = drug_list_df.filter(
            pl.col('id').map_elements(lambda x: node_id_to_category.get(x, '') == 'Drug', return_dtype=pl.Boolean)
        )
        logger.info(f"Loaded drug_list: {drug_list_df.shape}")

        disease_list_path = os.path.join(DATA_DIR, "raw_data", "disease_list_norm.tsv")
        if not os.path.exists(disease_list_path):
            raise FileNotFoundError(f"Disease list file not found: {disease_list_path}")
        disease_list_df = pl.read_csv(disease_list_path, separator="\t")
        disease_list_df = disease_list_df.filter(pl.col('id') != "['Error']") # Filter out erroneous entries
        disease_list_df = disease_list_df.filter(pl.col('id').is_in(node_embedding_dict.keys()))
        disease_list_df = disease_list_df.filter(
            pl.col('id').map_elements(lambda x: node_id_to_category.get(x, '') == 'Disease', return_dtype=pl.Boolean)
        )
        logger.info(f"Loaded disease_list: {disease_list_df.shape}")
        
        # Validate that required splits exist
        available_splits = processed_data_split_df['split'].unique().to_list()
        required_splits = ['TRAIN', 'VALID', 'TEST']
        missing_splits = [split for split in required_splits if split not in available_splits]
        if missing_splits:
            raise ValueError(f"Missing expected splits: {missing_splits}. Available splits: {available_splits}")
        
        # Check if all ground-truth pairs have embeddings and filter if needed
        pairs_with_embeddings = processed_data_split_df.filter(
            pl.col('drug_id').is_in(node_embedding_dict.keys()) & pl.col('dis_id').is_in(node_embedding_dict.keys())
        )
        missing_embedding_count = len(processed_data_split_df) - len(pairs_with_embeddings)
        if missing_embedding_count > 0:
            logger.warning(f"Filtering out {missing_embedding_count} ground-truth pairs due to missing embeddings.")
            processed_data_split_df = pairs_with_embeddings
        
        # Log data stats
        for split_name in required_splits:
            split_data = processed_data_split_df.filter(pl.col('split') == split_name)
            if not split_data.is_empty():
                pos_count = split_data.filter(pl.col('label') == 1).shape[0]
                neg_count = split_data.filter(pl.col('label') == 0).shape[0]
                logger.info(f"{split_name} set: {split_data.shape[0]} pairs ({pos_count} pos, {neg_count} neg)")

        return drug_list_df, disease_list_df, processed_data_split_df, node_embedding_dict, node_id_to_name, node_id_to_category, idx_map, bi_kg_edge

    except (FileNotFoundError, ValueError, pl.exceptions.PolarsError) as e:
        logger.error(f"Error loading raw data: {e}", exc_info=True)
        raise

def synthesize_soft_negatives(
    positive_pairs_df: pl.DataFrame, 
    all_drug_ids: list, 
    all_disease_ids: list, 
    all_known_pairs_set: set,
    n_neg_per_pos: int,
    soft_neg_weight: float
    ):
    """
    Generate synthetic "soft negative" pairs.
    For each positive pair, generate n_neg_per_pos soft negatives by deterministically
    generating half drug swaps and half disease swaps, ensuring the generated pair 
    is not in all_known_pairs_set.

    Args:
        positive_pairs_df (pl.DataFrame): DataFrame of positive pairs (must have 'drug_id', 'dis_id').
        all_drug_ids (list): List of all possible drug IDs to sample from.
        all_disease_ids (list): List of all possible disease IDs to sample from.
        all_known_pairs_set (set): A set of (drug_id, dis_id) tuples representing all known positive
                                   and hard negative pairs to avoid regenerating them as soft negatives.
        n_neg_per_pos (int): Number of soft negatives to generate per positive pair.
        soft_neg_weight (float): Weight to assign to these soft negative pairs.

    Returns:
        pl.DataFrame: DataFrame of soft negative pairs with columns 
                      ['drug_id', 'dis_id', 'label', 'weight'].
    """
    # If no positive pairs or no soft negatives to generate, return empty DataFrame
    if positive_pairs_df.is_empty() or not n_neg_per_pos:
        return pl.DataFrame(schema={'drug_id': pl.Utf8, 'dis_id': pl.Utf8, 'label': pl.Int8, 'weight': pl.Float32})

    logger.info(f"Generating {n_neg_per_pos} soft negatives per positive pair for {positive_pairs_df.shape[0]} positives...")
    
    # Calculate how many drug swaps vs disease swaps to generate
    n_drug_swaps = n_neg_per_pos // 2
    n_disease_swaps = n_neg_per_pos - n_drug_swaps  # Handle odd numbers
    
    logger.info(f"Strategy: {n_drug_swaps} drug swaps + {n_disease_swaps} disease swaps per positive pair")
    
    soft_neg_list = []
    max_attempts_per_pair = 100 # To prevent infinite loops if sampling is constrained

    for pos_row in tqdm(positive_pairs_df.iter_rows(named=True), total=positive_pairs_df.shape[0], desc="Synthesizing Soft Negatives"):
        drug_orig, dis_orig = pos_row['drug_id'], pos_row['dis_id']
        
        # Generate drug swaps (keep disease, change drug)
        for _ in range(n_drug_swaps):
            attempts = 0
            while attempts < max_attempts_per_pair:
                attempts += 1
                
                if len(all_drug_ids) > 1: # Ensure there's more than one drug to pick from
                    chosen_drug = np.random.choice(all_drug_ids)
                    while chosen_drug == drug_orig: # Ensure it's a different drug
                         chosen_drug = np.random.choice(all_drug_ids)
                    neg_drug, neg_dis = chosen_drug, dis_orig
                else:
                    break  # Can't generate drug swaps if only one drug available
                
                if (neg_drug, neg_dis) not in all_known_pairs_set:
                    soft_neg_list.append({'drug_id': neg_drug, 'dis_id': neg_dis, 'label': 0, 'weight': soft_neg_weight})
                    break # Found a valid soft negative
            if attempts == max_attempts_per_pair:
                logger.warning(f"Could not generate a unique drug swap for pair ({drug_orig}, {dis_orig}) after {max_attempts_per_pair} attempts.")
        
        # Generate disease swaps (keep drug, change disease)
        for _ in range(n_disease_swaps):
            attempts = 0
            while attempts < max_attempts_per_pair:
                attempts += 1
                
                if len(all_disease_ids) > 1: # Ensure there's more than one disease
                    chosen_dis = np.random.choice(all_disease_ids)
                    while chosen_dis == dis_orig: # Ensure it's a different disease
                        chosen_dis = np.random.choice(all_disease_ids)
                    neg_drug, neg_dis = drug_orig, chosen_dis
                else:
                    break  # Can't generate disease swaps if only one disease available
                
                if (neg_drug, neg_dis) not in all_known_pairs_set:
                    soft_neg_list.append({'drug_id': neg_drug, 'dis_id': neg_dis, 'label': 0, 'weight': soft_neg_weight})
                    break # Found a valid soft negative
            if attempts == max_attempts_per_pair:
                logger.warning(f"Could not generate a unique disease swap for pair ({drug_orig}, {dis_orig}) after {max_attempts_per_pair} attempts.")

    if not soft_neg_list:
        logger.warning("No soft negatives were generated.")
        return pl.DataFrame(schema={'drug_id': pl.Utf8, 'dis_id': pl.Utf8, 'label': pl.Int8, 'weight': pl.Float32})

    soft_neg_df = pl.DataFrame(soft_neg_list, schema={'drug_id': pl.Utf8, 'dis_id': pl.Utf8, 'label': pl.Int8, 'weight': pl.Float32})
    # Remove exact duplicates among the generated soft negatives themselves
    soft_neg_df = soft_neg_df.unique(subset=['drug_id', 'dis_id'], keep='first')
    logger.info(f"Generated {soft_neg_df.shape[0]} unique soft negative pairs.")
    return soft_neg_df

def create_feature_matrix(pairs_df: pl.DataFrame, node_embedding_dict: dict, bi_kg_edge: pl.DataFrame, idx_map: dict, k: int = 5):
    """
    Augment a DataFrame of pairs with concatenated drug and disease embeddings using 
    enhanced embedding approach with top-k similar node merging.

    Args:
        pairs_df (pl.DataFrame): DataFrame with 'drug_id' and 'dis_id' columns.
        node_embedding_dict (dict): Dictionary mapping node ID to embedding vector.
        bi_kg_edge (pl.DataFrame): Bidirectional knowledge graph edges.
        idx_map (dict): Mapping from node IDs to graph indices.
        k (int): Number of top similar nodes to use for merging (default: 5).

    Returns:
        pl.DataFrame: DataFrame with an added 'combined_embedding_vector' column
                      containing the concatenated enhanced drug and disease embeddings.
    """
    
    logger.info(f"Creating enhanced feature matrix for {pairs_df.shape[0]} pairs using batch-optimized approach with k={k}...")
    
    # Use the batch-optimized approach: eliminates redundant computations and disk I/O
    enhanced_embeddings, valid_indices = create_enhanced_embeddings_batch_optimized(
        pairs_df, bi_kg_edge, idx_map, node_embedding_dict, k
    )
    
    # Filter the original DataFrame to keep only valid pairs
    if len(valid_indices) < pairs_df.shape[0]:
        logger.warning(f"Dropped {pairs_df.shape[0] - len(valid_indices)} pairs due to missing embeddings or failed profile computation.")
        pairs_df_filtered = pairs_df.with_row_index().filter(pl.col("index").is_in(valid_indices)).drop("index")
    else:
        pairs_df_filtered = pairs_df
    
    # Add enhanced embeddings to the filtered DataFrame
    result_df = pairs_df_filtered.with_columns(
        pl.Series(name=DEFAULT_FEATURE_COL, values=enhanced_embeddings)
    )
    
    logger.info(f"Successfully created feature matrix with {result_df.shape[0]} pairs")
    return result_df


def main():
    """
    Main function to load data, generate initial soft negatives, 
    create the full feature matrix, and save it.
    """
    logger.info("Starting dataset preparation pipeline...")
    try:
        # 1. Load raw data components
        drug_list_df, disease_list_df, processed_data_split_df, node_embedding_dict, node_id_to_name, node_id_to_category, idx_map, bi_kg_edge = load_raw_data()

        # Prepare lists of all drug/disease IDs for negative sampling
        all_drug_ids = drug_list_df['id'].to_list()
        all_disease_ids = disease_list_df['id'].to_list()

        # Create a set of all known pairs (positives and hard negatives from original data)
        # to avoid sampling them as soft negatives.
        all_known_pairs_dict = {}
        all_known_pairs_dict['pos'] = set(
            (r['drug_id'], r['dis_id']) for r in 
            processed_data_split_df.select(['drug_id', 'dis_id', 'label']).iter_rows(named=True) if r['label'] == 1
        )
        all_known_pairs_dict['neg'] = set(
            (r['drug_id'], r['dis_id']) for r in 
            processed_data_split_df.select(['drug_id', 'dis_id', 'label']).iter_rows(named=True) if r['label'] == 0
        )
        logger.info(f"Created set of {len(all_known_pairs_dict['pos']) + len(all_known_pairs_dict['neg'])} known pairs (positives/hard negatives).")

        # Create the combined set of all known pairs for negative sampling exclusion
        all_known_pairs_set = all_known_pairs_dict['pos'].union(all_known_pairs_dict['neg'])

        # 2. Assign weights to original pairs
        # Hard positives (label=1) and hard negatives (label=0) get weight 1.0
        original_pairs_df = processed_data_split_df.with_columns(pl.lit(1.0).cast(pl.Float32).alias('weight'))
        # Select only the columns we need to match soft_neg_df schema
        original_pairs_df = original_pairs_df.select(['drug_id', 'dis_id', 'label', 'weight', 'split'])
        logger.info("Assigned weight 1.0 to original positive and hard negative pairs.")
        
        # 3. Generate initial soft negatives
        # Soft negatives are typically generated based on positive examples in the training set.
        # As per spec, "randomly swap drug or disease"
        train_positives_df = original_pairs_df.filter((pl.col('label') == 1) & (pl.col('split') == 'TRAIN'))
        if train_positives_df.is_empty() and N_SOFT_NEG_PER_POS > 0:
            logger.warning("No positive examples in the TRAIN split to base initial soft negative generation on.")
        
        soft_neg_df = synthesize_soft_negatives(
            positive_pairs_df=train_positives_df,
            all_drug_ids=all_drug_ids,
            all_disease_ids=all_disease_ids,
            all_known_pairs_set=all_known_pairs_set,
            n_neg_per_pos=N_SOFT_NEG_PER_POS,
            soft_neg_weight=SOFT_NEGATIVE_WEIGHT
        )
        
        # Add 'split' column to soft negatives; assign them to 'TRAIN' by default for initial dataset
        # Meta-learner trains on OOF predictions, so base models see these during their training.
        if not soft_neg_df.is_empty():
             soft_neg_df = soft_neg_df.with_columns(pl.lit('TRAIN').cast(pl.Categorical).alias('split'))
        
        logger.info(f"Initial dataset composition before adding soft negatives:")
        logger.info(f"  Positive examples: {original_pairs_df.filter(pl.col('label') == 1).shape[0]}")
        logger.info(f"  Hard negative examples: {original_pairs_df.filter(pl.col('label') == 0).shape[0]}")
        
        # 4. Combine original pairs with initial soft negatives
        if not soft_neg_df.is_empty():
            combined_df = pl.concat([original_pairs_df, soft_neg_df], how='vertical_relaxed')
            logger.info(f"Added {soft_neg_df.shape[0]} initial soft negatives. Total pairs now: {combined_df.shape[0]}")
        else:
            combined_df = original_pairs_df
            logger.info("No initial soft negatives added.")

        # 5. Create feature matrix (add enhanced embeddings)
        logger.info("Using direct edge DataFrame operations for enhanced embeddings (more efficient)")
        full_dataset_with_features_df = create_feature_matrix(
            combined_df, node_embedding_dict, bi_kg_edge, idx_map
        )

        if full_dataset_with_features_df.is_empty():
            logger.error("Resulting feature matrix is empty. Aborting.")
            return

        # 6. Save the full dataset with features
        output_dir = os.path.join(DATA_DIR, "processed_data")
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        output_path = os.path.join(output_dir, "full_dataset_with_features.parquet")
        full_dataset_with_features_df.write_parquet(output_path)
        logger.info(f"Full dataset with features saved to: {output_path} (Shape: {full_dataset_with_features_df.shape})")

        # Save supporting files (optional, as embeddings are now in the main file)
        # For dynamic sampling, might still need drug/disease IDs and the full known_pairs_set.
        with open(os.path.join(output_dir, "all_drug_ids.pkl"), "wb") as f:
            pickle.dump(all_drug_ids, f)
        with open(os.path.join(output_dir, "all_disease_ids.pkl"), "wb") as f:
            pickle.dump(all_disease_ids, f)
        with open(os.path.join(output_dir, "all_known_pairs_dict.pkl"), "wb") as f:
            pickle.dump(all_known_pairs_dict, f)
        with open(os.path.join(output_dir, "node_embedding_dict.pkl"), "wb") as f: # Save for potential inference use
            pickle.dump(node_embedding_dict, f)
            
        logger.info(f"Supporting files for dynamic sampling and inference saved to: {output_dir}")

        logger.info("Dataset preparation pipeline completed successfully.")

    except Exception as e:
        logger.error(f"Error in dataset preparation pipeline: {e}", exc_info=True)
        raise

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Prepare dataset for drug-disease repurposing ensemble model")
    parser.add_argument("--clear-kg-cache", action="store_true", 
                        help="Clear cached KG processing results before running (forces regeneration)")
    
    args = parser.parse_args()
    
    if args.clear_kg_cache:
        logger.info("Clearing KG cache as requested...")
        clear_kg_cache()
    
    main()
