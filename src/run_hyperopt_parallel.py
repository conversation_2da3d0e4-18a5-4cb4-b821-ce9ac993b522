"""
Orchestrator script specifically for running parallel hyperparameter optimization.

This script can be used as an alternative to the hyperopt step in run.py
when you want more control over the parallel optimization process.
"""

import os
import sys
import argparse
import time

# Add src to path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from config import LOGS_DIR
from utils import setup_logger, get_timestamp
import run_parallel_optimization
import apply_optimized_params

# Setup logging
timestamp = get_timestamp()
os.makedirs(LOGS_DIR, exist_ok=True)
log_file = os.path.join(LOGS_DIR, f"run_hyperopt_parallel_{timestamp}.log")
logger = setup_logger("hyperopt_parallel", log_file)

def main():
    parser = argparse.ArgumentParser(description="Run parallel hyperparameter optimization")
    parser.add_argument("--n_calls", type=int, default=50, help="Number of optimization calls per model")
    parser.add_argument("--apply_params", action="store_true", help="Automatically apply optimized parameters to config")
    parser.add_argument("--models", nargs='+', 
                       choices=['xgb_clf', 'lgbm_clf', 'catboost_clf', 'tabnet_clf'],
                       help="Specific models to optimize (default: all)")
    parser.add_argument('--model', type=str, required=True,
                        choices=['xgb_clf', 'lgbm_clf', 'catboost_clf', 'tabnet_clf'],
                        help='Model to optimize')
    
    args = parser.parse_args()
    
    logger.info("="*80)
    logger.info("Parallel Hyperparameter Optimization")
    logger.info("="*80)
    logger.info(f"Number of calls per model: {args.n_calls}")
    logger.info(f"Models to optimize: {args.models if args.models else 'all'}")
    logger.info(f"Auto-apply parameters: {args.apply_params}")
    
    start_time = time.time()
    
    try:
        # Run parallel optimization
        logger.info("Starting parallel hyperparameter optimization...")
        results = run_parallel_optimization.main()
        
        # Check results
        successful = [r for r in results if r['success']]
        failed = [r for r in results if not r['success']]
        
        logger.info(f"Optimization completed in {time.time() - start_time:.2f} seconds")
        logger.info(f"Successful: {len(successful)}/4 classifier models")
        
        if failed:
            logger.warning(f"Failed models: {[r['script'] for r in failed]}")
        
        # Apply parameters if requested and we have successful results
        if args.apply_params and successful:
            logger.info("Applying optimized parameters to config...")
            apply_start = time.time()
            
            success = apply_optimized_params.main()
            if success:
                logger.info(f"Parameters applied successfully in {time.time() - apply_start:.2f} seconds")
            else:
                logger.error("Failed to apply optimized parameters")
        
        logger.info("="*80)
        logger.info("Hyperparameter Optimization Completed.")
        logger.info("="*80)
        
        if successful:
            logger.info("✓ You can now run base model training with optimized parameters:")
            logger.info("  python run.py --step train_base --skip_hyperopt")
        
    except Exception as e:
        logger.error(f"Error during hyperparameter optimization: {e}", exc_info=True)
        return False
    
    return True

if __name__ == "__main__":
    main() 