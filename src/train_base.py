# train_base.py
"""
Training module for base learners in the drug-disease repurposing ensemble model.

This script:
1. Loads the feature matrix prepared by prepare_dataset.py.
2. Splits data into TRAIN, VALID, and TEST sets based on the 'split' column.
3. Trains 4 base classifier models on the COMPLETE TRAIN set, using the VALIDATION set for early stopping:
   - XGBClassifier (binary:logistic)
   - LGBMClassifier (binary)
   - CatBoostClassifier
   - TabNetClassifier
4. Saves predictions for meta-model training:
   - TRAINING: Only pairs whose are known (filtered for meta-learning)
   - VALIDATION: All validation pairs (no filtering)
5. Saves all trained base models.

Usage:
    python train_base.py
"""

import os
import pandas as pd
import numpy as np

import pickle
import polars as pl
from copy import deepcopy
from tqdm import tqdm
import xgboost as xgb
import lightgbm as lgb
import catboost as cb
from joblib import dump

from config import (
    SEED, LOGS_DIR, DATA_DIR, MODELS_DIR,
    XGB_CLF_PARAMS, LGBM_CLF_PARAMS, 
    CATBOOST_CLF_PARAMS, TABNET_CLF_PARAMS,
    DEFAULT_FEATURE_COL
)

from utils import (
    setup_logger, get_timestamp
)

# Setup logging
timestamp = get_timestamp()
os.makedirs(LOGS_DIR, exist_ok=True)
os.makedirs(MODELS_DIR, exist_ok=True)

BASE_MODELS_SAVE_DIR = os.path.join(MODELS_DIR, "base_models")
TRAIN_VAL_PREDICTIONS_DIR = os.path.join(DATA_DIR, "train_val_set_predictions")
os.makedirs(BASE_MODELS_SAVE_DIR, exist_ok=True)
os.makedirs(TRAIN_VAL_PREDICTIONS_DIR, exist_ok=True)

log_file = os.path.join(LOGS_DIR, f"train_base_{timestamp}.log")
logger = setup_logger("train_base", log_file)

np.random.seed(SEED)

# TabNet-specific functions
# Import centralized TabNet utilities  
from tabnet_utils import (
    is_tabnet_available,
    create_tabnet_model
)

# Global cache for data needed by dynamic negative sampling, loaded once
NODE_EMBEDDING_DICT_CACHE = {}

def get_node_embedding_dict():
    if not NODE_EMBEDDING_DICT_CACHE:
        embedding_dict_path = os.path.join(DATA_DIR, "processed_data", "node_embedding_dict.pkl")
        try:
            with open(embedding_dict_path, "rb") as f:
                NODE_EMBEDDING_DICT_CACHE['data'] = pickle.load(f)
            logger.info(f"Loaded node_embedding_dict with {len(NODE_EMBEDDING_DICT_CACHE['data'])} entries.")
        except FileNotFoundError:
            logger.error(f"node_embedding_dict.pkl not found at {embedding_dict_path}. Required for DNS feature creation.")
            raise
    return NODE_EMBEDDING_DICT_CACHE['data']

def load_and_split_prepared_dataset():
    """
    Load the main dataset and split it into TRAIN, VALID, TEST based on 'split' column.
    """
    file_path = os.path.join(DATA_DIR, "processed_data", "full_dataset_with_features.parquet")
    logger.info(f"Loading full dataset with features from: {file_path}")

    data_pl = pl.read_parquet(file_path)
    logger.info(f"Loaded dataset with shape: {data_pl.shape}")

    if DEFAULT_FEATURE_COL not in data_pl.columns:
        logger.error(f"Feature column '{DEFAULT_FEATURE_COL}' not found in the dataset.")
        raise ValueError(f"Missing feature column '{DEFAULT_FEATURE_COL}'.")

    # Ensure correct dtypes
    type_mapping = {}
    if 'label' in data_pl.columns:
        type_mapping['label'] = pl.Int32
    if 'weight' in data_pl.columns:
        type_mapping['weight'] = pl.Float64

    if type_mapping:
        data_pl = data_pl.with_columns([
            pl.col(col_name).cast(dtype) for col_name, dtype in type_mapping.items()
        ])
        
    if 'split' not in data_pl.columns:
        logger.error("The 'split' column is missing from the dataset. Cannot perform train/val/test split.")
        raise ValueError("Missing 'split' column in the loaded dataset.")

    train_df = data_pl.filter(pl.col('split') == 'TRAIN')
    val_df = data_pl.filter(pl.col('split') == 'VALID')
    test_df = data_pl.filter(pl.col('split') == 'TEST')

    logger.info(f"Data split: TRAIN {train_df.shape}, VALID {val_df.shape}, TEST {test_df.shape}")
    if train_df.is_empty():
        logger.warning("TRAIN split is empty. Training cannot proceed.")
    if val_df.is_empty():
        logger.warning("VAL split is empty. Early stopping and meta-learner input generation might be affected.")

    return train_df, val_df, test_df


def prepare_training_data(train_df_original_pl: pl.DataFrame):
    """
    Prepares training data for model input.
    Returns NumPy arrays for model training (X_train, y_train, w_train).
    """
    train_df_fold_pl = train_df_original_pl.clone()

    # Prepare final NumPy arrays for models
    feature_list = train_df_fold_pl[DEFAULT_FEATURE_COL].to_list()
    X_train = np.array(feature_list)
        
    y_train = train_df_fold_pl['label'].to_numpy()
    w_train = train_df_fold_pl['weight'].to_numpy()
    
    return X_train, y_train, w_train


def train_xgb_classifier(X_train, y_train, w_train, X_valid, y_valid, w_valid, params):
    logger.info("Training XGBoost Classifier...")
    # Extract early_stopping_rounds from params and pass to constructor
    xgb_params = params.copy()
    early_stopping_rounds = xgb_params.pop("early_stopping_rounds", 50)
    verbose_eval = xgb_params.pop("verbose_eval", False)

    model = xgb.XGBClassifier(early_stopping_rounds=early_stopping_rounds, **xgb_params)
    eval_set_list = [(X_valid, y_valid)]

    model.fit(X_train, y_train, sample_weight=w_train,
              eval_set=eval_set_list,
              sample_weight_eval_set=[w_valid],
              verbose=verbose_eval)

    y_train_pred_proba = model.predict_proba(X_train)[:, 1]
    y_valid_pred_proba = model.predict_proba(X_valid)[:, 1]

    return model, y_train_pred_proba, y_valid_pred_proba

def train_lgbm_classifier(X_train, y_train, w_train, X_valid, y_valid, w_valid, params):
    logger.info("Training LightGBM Classifier...")
    model = lgb.LGBMClassifier(**params)
    callbacks = [lgb.early_stopping(stopping_rounds=params.get("early_stopping_rounds",50), verbose=params.get("verbose", -1))]
    eval_metric_lgbm = params.get("metric", "binary_logloss")
    eval_set_list = [(X_valid, y_valid)]

    model.fit(X_train, y_train, sample_weight=w_train,
              eval_set=eval_set_list,
              eval_sample_weight=[w_valid],
              eval_metric=eval_metric_lgbm,
              callbacks=callbacks)

    y_train_pred_proba = model.predict_proba(X_train)[:, 1]
    y_valid_pred_proba = model.predict_proba(X_valid)[:, 1]

    return model, y_train_pred_proba, y_valid_pred_proba

def train_catboost_classifier(X_train, y_train, w_train, X_valid, y_valid, w_valid, params):
    logger.info("Training CatBoost Classifier...")
    logger.info("CatBoost configured to use CPU for training consistency")

    # Create the CatBoost model
    model = cb.CatBoostClassifier(**params)

    # Prepare evaluation set for early stopping
    eval_set = cb.Pool(X_valid, y_valid, weight=w_valid)

    # Fit the model
    model.fit(X_train, y_train, sample_weight=w_train, eval_set=eval_set)

    y_train_pred_proba = model.predict_proba(X_train)[:, 1]
    y_valid_pred_proba = model.predict_proba(X_valid)[:, 1]

    return model, y_train_pred_proba, y_valid_pred_proba

def train_tabnet_classifier(X_train, y_train, w_train, X_valid, y_valid, w_valid, params):
    logger.info("Training TabNet Classifier...")
    
    # Check if TabNet is available
    if not is_tabnet_available():
        logger.error("TabNet not available")
        raise RuntimeError("TabNet not available")
    
    # Create TabNet model using centralized utilities
    model, training_params = create_tabnet_model(
        model_type='classifier',
        base_params=params,
        gpu_id=None  # Use default from config (GPU_DEVICE_ID_TABNET)
    )
    
    # Fit the model with sample weights and early stopping
    logger.info("TabNet: Training with sample weights and early stopping")
    model.fit(
        X_train, y_train,
        eval_set=[(X_valid, y_valid)],
        weights=w_train,  # TabNet supports sample weights!
        eval_metric=['auc'],  # Use AUC for binary classification
        max_epochs=training_params.get('max_epochs', 200),
        patience=training_params.get('patience', 15),
        batch_size=training_params.get('batch_size', 1024),
        virtual_batch_size=training_params.get('virtual_batch_size', 128)
    )
    
    # Get predictions
    y_train_pred_proba = model.predict_proba(X_train)[:, 1]
    y_valid_pred_proba = model.predict_proba(X_valid)[:, 1]

    return model, y_train_pred_proba, y_valid_pred_proba

def train_all_base_models(train_df_pl: pl.DataFrame, val_df_pl: pl.DataFrame, runtime_params: dict):
    logger.info("===== Training All Base Models using Fixed TRAIN/VALID Split =====")

    X_train, y_train, w_train = prepare_training_data(train_df_pl)

    # Prepare validation data (NumPy arrays)
    feature_list_valid = val_df_pl[DEFAULT_FEATURE_COL].to_list()
    X_valid = np.array(feature_list_valid)
    y_valid = val_df_pl['label'].to_numpy()
    w_valid = val_df_pl['weight'].to_numpy()

    # Filter training data for meta-learning
    # Note: Model training still uses complete training data (X_train, y_train, w_train)
    train_weight_filter = train_df_pl['weight'] == 1.0
    train_filtered_df = train_df_pl.filter(train_weight_filter)

    logger.info(f"Training data for meta-learning: {train_filtered_df.shape[0]} pairs (weight == 1.0) out of {train_df_pl.shape[0]} total training pairs")
    logger.info(f"Validation data for meta-learning: {val_df_pl.shape[0]} pairs (all validation pairs)")

    # Prepare training predictions structure (weight == 1.0 pairs only for meta-learning)
    train_set_predictions = {
        'labels': train_filtered_df['label'].to_numpy(),
        'drug_ids': train_filtered_df['drug_id'].to_numpy(),
        'dis_ids': train_filtered_df['dis_id'].to_numpy(),
        'split': np.full(train_filtered_df.shape[0], 'TRAIN', dtype=object)
    }

    # Prepare validation predictions structure (all validation pairs)
    val_set_predictions = {
        'labels': y_valid,
        'drug_ids': val_df_pl['drug_id'].to_numpy(),
        'dis_ids': val_df_pl['dis_id'].to_numpy(),
        'split': np.full(len(y_valid), 'VALID', dtype=object)
    }

    models_to_train = [
        ("xgb_clf", train_xgb_classifier, runtime_params['xgb_clf'], 'proba', [X_train, y_train, w_train, X_valid, y_valid, w_valid]),
        ("lgbm_clf", train_lgbm_classifier, runtime_params['lgbm_clf'], 'proba', [X_train, y_train, w_train, X_valid, y_valid, w_valid]),
        ("catboost_clf", train_catboost_classifier, runtime_params['catboost_clf'], 'proba', [X_train, y_train, w_train, X_valid, y_valid, w_valid]),
        ("tabnet_clf", train_tabnet_classifier, runtime_params['tabnet_clf'], 'proba', [X_train, y_train, w_train, X_valid, y_valid, w_valid])
    ]

    logger.info("Preparing drug-disease evaluation data...")
    # if not os.path.exists(os.path.join(DATA_DIR, "processed_data", "drug_disease_data_for_base_models.parquet")):
    # drug_disease_data = prepare_drug_disease_evaluation_data(val_df_pl)
    # # save drug_disease_data to parquet
    # drug_disease_data.write_parquet(os.path.join(DATA_DIR, "processed_data", "drug_disease_data_for_base_models.parquet"))
    # else:
    #     drug_disease_data = pl.read_parquet(os.path.join(DATA_DIR, "processed_data", "drug_disease_data_for_base_models.parquet"))

    logger.info(f"Starting training for {len(models_to_train)} base models...")
    for name, train_func, model_params, prediction_type, train_args in tqdm(models_to_train, desc="Training base models"):
        current_train_args = list(train_args)
        model, train_pred_output, val_pred_output = train_func(*current_train_args, model_params)

        dump(model, os.path.join(BASE_MODELS_SAVE_DIR, f"{name}.joblib"))
        logger.info(f"Saved base model: {name} to {BASE_MODELS_SAVE_DIR}")

        # Filter training predictions to only include weight == 1 pairs for meta-learning
        # Get indices of weight == 1 pairs in the original training data
        weight_selected_indices = np.where(train_df_pl['weight'].to_numpy() == 1.0)[0]
        train_pred_filtered = train_pred_output[weight_selected_indices]

        # Collect filtered training predictions and all validation predictions
        train_set_predictions[f'{name}_{prediction_type}'] = train_pred_filtered
        val_set_predictions[f'{name}_{prediction_type}'] = val_pred_output
        
        # if val_pred_output is not None and y_valid is not None and y_valid.shape[0] > 0 and not np.all(np.isnan(val_pred_output)):
        #     # Prepare drug-disease evaluation data
        #     copy_drug_disease_data = deepcopy(drug_disease_data)
        #     X = np.array(copy_drug_disease_data[DEFAULT_FEATURE_COL].to_list())
        #     # All models are now classifiers, so use predict_proba
        #     y_pred_proba = model.predict_proba(X)[:, 1]
            
        #     # add y_pred_proba to copy_drug_disease_data
        #     copy_drug_disease_data = copy_drug_disease_data.with_columns(pl.lit(y_pred_proba).alias("treat score"))
            
        #     eval_metrics = evaluate_predictions(copy_drug_disease_data)
        #     logger.info(f"{name.upper()} VALID Classification Metrics: ")
        #     logger.info(f"Accuracy: {eval_metrics['accuracy']:.4f}")
        #     logger.info(f"Precision: {eval_metrics['precision']:.4f}")
        #     logger.info(f"Recall: {eval_metrics['recall']:.4f}")
        #     logger.info(f"F1: {eval_metrics['f1']:.4f}")
        #     logger.info(f"PR-AUC: {eval_metrics['pr_auc']:.4f}")
        #     logger.info(f"LogLoss: {eval_metrics['logloss']:.4f}")
            
        #     logger.info(f"{name.upper()} VALID Ranking Metrics: ")
        #     logger.info(f"MRR: {eval_metrics['mrr']:.4f}")
        #     logger.info(f"Hit@1: {eval_metrics['hit@1']:.4f}")
        #     logger.info(f"Hit@5: {eval_metrics['hit@5']:.4f}")
        #     logger.info(f"Hit@10: {eval_metrics['hit@10']:.4f}")
        #     logger.info(f"Hit@20: {eval_metrics['hit@20']:.4f}")
        #     logger.info(f"Hit@100: {eval_metrics['hit@100']:.4f}")
        #     logger.info(f"Recall@20000: {eval_metrics['recall@20000']:.4f}")
        #     logger.info(f"Recall@40000: {eval_metrics['recall@40000']:.4f}")
        #     logger.info(f"Recall@60000: {eval_metrics['recall@60000']:.4f}")
        #     logger.info(f"Recall@80000: {eval_metrics['recall@80000']:.4f}")
        #     logger.info(f"Recall@100000: {eval_metrics['recall@100000']:.4f}")
            
        #     # save memory
        #     del copy_drug_disease_data
            
        # else:
        #      logger.info(f"No validation data or predictions for {name.upper()}, metrics not computed.")

    logger.info("===== Finished Training All Base Models =====")

    # Combine training and validation predictions into a single DataFrame
    train_df_predictions = pl.DataFrame(train_set_predictions)
    val_df_predictions = pl.DataFrame(val_set_predictions)

    # Concatenate training and validation predictions
    combined_predictions = pl.concat([train_df_predictions, val_df_predictions], how="vertical")

    logger.info(f"Combined predictions shape: {combined_predictions.shape} (Train filtered: {train_df_predictions.shape[0]}, Valid: {val_df_predictions.shape[0]})")
    logger.info("Note: Training predictions filtered to weight == 1.0 pairs only for meta-learning")

    return combined_predictions


def main():
    logger.info("Starting base model training pipeline using fixed TRAIN/VALID splits ...")
    logger.info("Note: This training uses parameters from config.py")
    logger.info("If you've run hyperparameter optimization, ensure you've applied the optimized parameters first.")
    
    train_df_pl, val_df_pl, test_df_pl = load_and_split_prepared_dataset()

    if train_df_pl.is_empty():
        logger.error("TRAIN dataset is empty. Aborting base model training.")
        return

    # Ensure 'pair_id' exists for saving predictions, if not, create it
    for df_name, df_obj in [("train_df_pl", train_df_pl), ("val_df_pl", val_df_pl)]:
        current_df = df_obj
        if not current_df.is_empty() and 'pair_id' not in current_df.columns and \
           'drug_id' in current_df.columns and 'dis_id' in current_df.columns:
            current_df = current_df.with_columns(
                (pl.col("drug_id").cast(pl.Utf8) + "_" + pl.col("dis_id").cast(pl.Utf8)).alias("pair_id")
            )
            if df_name == "train_df_pl": 
                train_df_pl = current_df
            elif df_name == "val_df_pl": 
                val_df_pl = current_df

    runtime_params = {
        'xgb_clf': XGB_CLF_PARAMS,
        'lgbm_clf': LGBM_CLF_PARAMS,
        'catboost_clf': CATBOOST_CLF_PARAMS,
        'tabnet_clf': TABNET_CLF_PARAMS
    }

    train_val_pred_df_pl = train_all_base_models(
        train_df_pl, val_df_pl, runtime_params
    )

    train_val_pred_output_path = os.path.join(TRAIN_VAL_PREDICTIONS_DIR, "train_val_set_base_predictions.parquet")
    train_val_pred_df_pl.write_parquet(train_val_pred_output_path)
    logger.info(f"Training and validation set base model predictions saved to: {train_val_pred_output_path} (Shape: {train_val_pred_df_pl.shape})")

    logger.info(f"Test set data (shape: {test_df_pl.shape}) is loaded and available for a final evaluation step later.")
    logger.info("Base model training pipeline completed.")


if __name__ == "__main__":
    main()
