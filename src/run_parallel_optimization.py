"""
Script to run all hyperparameter optimizations in parallel.
"""

import os
import subprocess
import sys
from concurrent.futures import ProcessPoolExecutor, as_completed
import time
import argparse

def run_optimization_script(script_name, n_calls=50):
    """Run a single optimization script with specified number of calls."""
    try:
        print(f"Starting {script_name} with {n_calls} calls...")
        start_time = time.time()
        
        # Pass n_calls as command line argument to the script
        result = subprocess.run([
            sys.executable, script_name, "--n_calls", str(n_calls)
        ], capture_output=True, text=True, cwd=os.path.dirname(os.path.abspath(__file__)))
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ {script_name} completed successfully in {duration:.2f}s")
            return {
                'script': script_name,
                'success': True,
                'duration': duration,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
        else:
            print(f"❌ {script_name} failed with return code {result.returncode}")
            print(f"Error output: {result.stderr}")
            return {
                'script': script_name,
                'success': False,
                'duration': duration,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
    except Exception as e:
        print(f"❌ Error running {script_name}: {e}")
        return {
            'script': script_name,
            'success': False,
            'duration': 0,
            'error': str(e)
        }

def main(n_calls=50):
    """Run optimization scripts in parallel."""
    optimization_scripts = [
        'optimize_xgb_clf.py',
        'optimize_lgbm_clf.py',
        'optimize_catboost_clf.py',
        'optimize_tabnet_clf.py'
    ]
    
    print("Running optimization for all 4 classifier models")
    print(f"Starting parallel hyperparameter optimization for {len(optimization_scripts)} models...")
    print(f"Number of calls per model: {n_calls}")
    
    start_time = time.time()
    results = []
    
    # Run all scripts in parallel
    with ProcessPoolExecutor(max_workers=len(optimization_scripts)) as executor:
        # Submit all jobs with n_calls parameter
        future_to_script = {
            executor.submit(run_optimization_script, script, n_calls): script 
            for script in optimization_scripts
        }
        
        # Collect results as they complete
        for future in as_completed(future_to_script):
            script = future_to_script[future]
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                print(f"❌ Exception in {script}: {e}")
                results.append({
                    'script': script,
                    'success': False,
                    'duration': 0,
                    'error': str(e)
                })
    
    end_time = time.time()
    total_duration = end_time - start_time
    
    # Print summary
    print("\n" + "="*80)
    print("PARALLEL HYPERPARAMETER OPTIMIZATION SUMMARY")
    print("="*80)
    
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    print(f"Total time: {total_duration:.2f}s")
    print(f"Successful optimizations: {len(successful)}/4")
    print(f"Failed optimizations: {len(failed)}/4")
    
    if successful:
        print("\n✅ Successful optimizations:")
        for result in successful:
            print(f"  - {result['script']}: {result['duration']:.2f}s")
    
    if failed:
        print("\n❌ Failed optimizations:")
        for result in failed:
            print(f"  - {result['script']}: {result.get('error', 'Unknown error')}")
    
    print(f"\nResults saved in: {os.path.join('..', 'models', 'hyperparameter_optimization')}")
    
    return results

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run parallel hyperparameter optimization")
    parser.add_argument("--n_calls", type=int, default=50, help="Number of optimization calls per model")
    
    args = parser.parse_args()
    results = main(n_calls=args.n_calls) 