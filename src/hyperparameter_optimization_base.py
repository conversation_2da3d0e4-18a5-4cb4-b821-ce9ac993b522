"""
Base functions for hyperparameter optimization across different models.

This module provides shared functionality for loading data and saving results
used by all model-specific optimization scripts.
"""

import os
import pickle
import numpy as np
import polars as pl
from pathlib import Path

from config import DATA_DIR, MODELS_DIR, LOGS_DIR, DEFAULT_FEATURE_COL
from utils import setup_logger, get_timestamp

# Setup logging
timestamp = get_timestamp()
os.makedirs(LOGS_DIR, exist_ok=True)
log_file = os.path.join(LOGS_DIR, f"hyperopt_base_{timestamp}.log")
logger = setup_logger("hyperopt_base", log_file)

def load_prepared_data():
    """
    Load the prepared dataset for hyperparameter optimization.
    
    Returns:
        tuple: (X, y, weights) where:
            - X: Feature matrix (numpy array)
            - y: Labels (numpy array)
            - weights: Sample weights (numpy array)
    """
    logger.info("Loading prepared data for hyperparameter optimization...")
    
    # Load the full dataset with features
    data_file = os.path.join(DATA_DIR, "processed_data", "full_dataset_with_features.parquet")
    
    if not os.path.exists(data_file):
        raise FileNotFoundError(f"Prepared dataset not found: {data_file}")
    
    logger.info(f"Loading dataset from: {data_file}")
    data_pl = pl.read_parquet(data_file)
    logger.info(f"Loaded dataset with shape: {data_pl.shape}")
    
    # Filter to training data only for optimization
    train_data = data_pl.filter(pl.col('split') == 'TRAIN')
    logger.info(f"Training data shape: {train_data.shape}")
    
    if train_data.is_empty():
        raise ValueError("No training data found in the dataset")
    
    # Extract features, labels, and weights
    try:
        # Extract feature vectors
        feature_list = train_data[DEFAULT_FEATURE_COL].to_list()
        X = np.array(feature_list)
        logger.info(f"Feature matrix shape: {X.shape}")
        
        # Extract labels
        y = train_data['label'].to_numpy()
        logger.info(f"Labels shape: {y.shape}")
        
        # Extract weights
        weights = train_data['weight'].to_numpy()
        logger.info(f"Weights shape: {weights.shape}")
        
        # Validate data
        if len(X) != len(y) or len(y) != len(weights):
            raise ValueError(f"Mismatched data lengths: X={len(X)}, y={len(y)}, weights={len(weights)}")
        
        logger.info(f"Successfully loaded {len(X)} samples for optimization")
        logger.info(f"Label distribution: {np.bincount(y)}")
        logger.info(f"Weight range: [{weights.min():.4f}, {weights.max():.4f}]")
        
        return X, y, weights
        
    except Exception as e:
        logger.error(f"Error extracting data from dataset: {e}")
        raise

def save_optimization_results(optimization_result, model_name):
    """
    Save optimization results to a pickle file.
    
    Args:
        optimization_result (dict): Dictionary containing optimization results
        model_name (str): Name of the model (e.g., 'xgb_clf', 'lgbm_clf')
    """
    # Create optimization results directory
    opt_results_dir = os.path.join(MODELS_DIR, "hyperparameter_optimization")
    os.makedirs(opt_results_dir, exist_ok=True)
    
    # Create filename with timestamp
    timestamp = get_timestamp()
    filename = f"{model_name}_optimization_result_{timestamp}.pkl"
    filepath = os.path.join(opt_results_dir, filename)
    
    # Save results
    try:
        with open(filepath, 'wb') as f:
            pickle.dump(optimization_result, f)
        logger.info(f"Optimization results saved to: {filepath}")
        return filepath
    except Exception as e:
        logger.error(f"Error saving optimization results: {e}")
        raise

def find_latest_optimization_results():
    """Find the most recent optimization results files for ALL 4 classifier models."""
    import glob
    
    opt_dir = os.path.join(MODELS_DIR, "hyperparameter_optimization")
    
    if not os.path.exists(opt_dir):
        raise FileNotFoundError(f"Optimization directory not found: {opt_dir}")
    
    # Model names to look for
    model_names = ['xgb_clf', 'lgbm_clf', 'catboost_clf', 'tabnet_clf']
    
    result_files = {}
    missing_models = []
    
    for model_name in model_names:
        # Find all result files for this model
        pattern = os.path.join(opt_dir, f"{model_name}_optimization_result_*.pkl")
        files = glob.glob(pattern)
        
        if files:
            # Get the most recent file
            latest_file = max(files, key=lambda f: os.path.getctime(f))
            result_files[model_name] = latest_file
            logger.info(f"Found optimization results for {model_name}: {os.path.basename(latest_file)}")
        else:
            missing_models.append(model_name)
    
    if missing_models:
        logger.error(f"Missing optimization results for models: {missing_models}")
        raise FileNotFoundError(f"All 4 classifier models must have optimization results. Missing: {missing_models}")
    
    if len(result_files) != 4:
        raise FileNotFoundError(f"Expected 4 model results, found {len(result_files)}")
    
    logger.info("✓ Found optimization results for all 4 classifier models.")
    return result_files

def load_optimization_results(results_files=None):
    """Load optimization results from individual model files."""
    if results_files is None:
        results_files = find_latest_optimization_results()
    
    logger.info("Loading optimization results from individual model files...")
    
    combined_results = {}
    
    for model_name, file_path in results_files.items():
        try:
            logger.info(f"Loading {model_name} results from: {os.path.basename(file_path)}")
            with open(file_path, "rb") as f:
                result = pickle.load(f)
            combined_results[model_name] = result
        except Exception as e:
            logger.error(f"Error loading results for {model_name}: {e}")
            combined_results[model_name] = None
    
    return combined_results 