"""
Main orchestrator script for the Drug-Disease Repurposing Ensemble Model.

This script provides a command-line interface to run the full pipeline:
1. Process Split Data: Calls src.process_split_data.main() - Creates train/valid/test splits from raw data
2. Data Preparation: Calls src.prepare_dataset.main() - Prepares features and embeddings
3. Hyperparameter Optimization: Runs parallel optimization for all 4 models (optional)
4. Apply Optimized Parameters: Calls src.apply_optimized_params.main() (if step 3 was run)
5. Base Model Training: Calls src.train_base.main()
6. Meta-Learner Training: Calls src.train_meta.main() (uses combined training+validation predictions)
7. Full Evaluation: Run inference on a test set and report final metrics.

Usage examples:
  python run.py --step all                          # Run all steps including hyperparameter optimization
  python run.py --step all --skip_hyperopt          # Run all steps except hyperparameter optimization
  python run.py --step process_split                # Run only data split processing
  python run.py --step prepare_data                 # Run only data preparation
  python run.py --step hyperopt                     # Run only hyperparameter optimization (for all 4 models)
  python run.py --step apply_params                 # Apply optimized parameters to config
  python run.py --step train_base                   # Run base model training
  python run.py --step train_meta                   # Run meta-learner training
  python run.py --step quick                        # Quick run: process_split + prepare_data + train_base (skip optimization)
  python run.py --step optimize_only                # Only run hyperparameter optimization and apply parameters
"""

import os
import sys
import argparse
import time

# Ensure src directory is in Python path to import modules from it
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = current_dir
SRC_DIR = os.path.join(project_root, "src")
if SRC_DIR not in sys.path:
    sys.path.insert(0, SRC_DIR)

from config import LOGS_DIR, DATA_DIR, MODELS_DIR
from utils import setup_logger, get_timestamp

import prepare_dataset
import process_split_data
import train_base
import train_meta

# Setup main logger for this run.py script
run_timestamp = get_timestamp()
os.makedirs(LOGS_DIR, exist_ok=True)
run_log_file = os.path.join(LOGS_DIR, f"run_pipeline_{run_timestamp}.log")
logger = setup_logger("run_pipeline", run_log_file)


def check_hyperopt_results_exist():
    """
    Check if hyperparameter optimization results exist for ALL 4 classifier models.
    
    Returns:
        tuple: (all_models_found, found_models, missing_models)
    """
    opt_dir = os.path.join(MODELS_DIR, "hyperparameter_optimization")
    if not os.path.exists(opt_dir):
        return False, [], []
    
    # Check for individual model result files
    model_names = ['xgb_clf', 'lgbm_clf', 'catboost_clf', 'tabnet_clf']
    
    found_models = []
    missing_models = []
    
    for model_name in model_names:
        result_files = [f for f in os.listdir(opt_dir) 
                       if f.startswith(f"{model_name}_optimization_result_") and f.endswith(".pkl")]
        if result_files:
            found_models.append(model_name)
        else:
            missing_models.append(model_name)
    
    all_models_found = len(found_models) == 4
    
    if all_models_found:
        logger.info("✓ Found optimization results for all 4 classifier models.")
    else:
        logger.warning(f"Found optimization results for {len(found_models)}/4 models: {found_models}")
        logger.warning(f"Missing optimization results for models: {missing_models}")
        logger.warning("All 4 classifier models must be optimized to apply parameters.")
    
    return all_models_found, found_models, missing_models


def main_pipeline(args):
    """Executes the selected pipeline steps."""
    start_time_total = time.time()
    logger.info(f"Starting pipeline execution with arguments: {args}")
    logger.info("="*80)
    logger.info("Drug-Disease Repurposing Ensemble Model Pipeline")
    logger.info("="*80)

    # ========== 1. Process Split Data ========== 
    if ("all" in args.step or "process_split" in args.step or "quick" in args.step):
        if args.skip_process_split or args.skip_data_prep:
            logger.info("Skipping Process Split Data step as per --skip_process_split or --skip_data_prep.")
        else:
            logger.info("--- STEP 1: Process Split Data ---")
            step_start_time = time.time()
            try:
                process_split_data.main()
                logger.info(f"✓ Process Split Data completed in {time.time() - step_start_time:.2f} seconds.")
            except Exception as e:
                logger.error(f"✗ Error during Process Split Data: {e}", exc_info=True)
                logger.critical("Pipeline halted due to error in Process Split Data.")
                return

    # ========== 2. Data Preparation ========== 
    if ("all" in args.step or "prepare_data" in args.step or "quick" in args.step):
        if args.skip_data_prep:
            logger.info("Skipping Data Preparation step as per --skip_data_prep.")
        else:
            logger.info("--- STEP 2: Data Preparation ---")
            step_start_time = time.time()
            try:
                prepare_dataset.main()
                logger.info(f"✓ Data Preparation completed in {time.time() - step_start_time:.2f} seconds.")
            except Exception as e:
                logger.error(f"✗ Error during Data Preparation: {e}", exc_info=True)
                logger.critical("Pipeline halted due to error in Data Preparation.")
                return

    # ========== 3. Hyperparameter Optimization ========== 
    if ("all" in args.step or "hyperopt" in args.step or "optimize_only" in args.step):
        if args.skip_hyperopt:
            logger.info("Skipping Hyperparameter Optimization step as per --skip_hyperopt.")
        else:
            logger.info("--- STEP 3: Hyperparameter Optimization ---")
            logger.info("Running parallel hyperparameter optimization for all 4 classifier models...")
            logger.info("This step may take 1-3 hours depending on your hardware...")
            step_start_time = time.time()
            try:
                # Import and run parallel hyperparameter optimization
                import run_parallel_optimization
                results = run_parallel_optimization.main(n_calls=args.n_calls)
                
                # Check if ALL optimizations were successful
                successful_results = [r for r in results if r['success']]
                if len(successful_results) == 4:
                    logger.info(f"✓ Hyperparameter Optimization completed in {time.time() - step_start_time:.2f} seconds.")
                    logger.info("Successfully optimized all 4 classifier models.")
                else:
                    failed_results = [r for r in results if not r['success']]
                    failed_models = [r['script'].replace('optimize_', '').replace('.py', '') for r in failed_results]
                    logger.warning(f"✗ Hyperparameter optimization failed for {len(failed_results)} models: {failed_models}")
                    logger.warning("All 4 classifier models must be successfully optimized. Please check the logs and retry.")
                    
            except Exception as e:
                logger.error(f"✗ Error during Hyperparameter Optimization: {e}", exc_info=True)
                logger.warning("Continuing pipeline with default parameters...")

    # ========== 4. Apply Optimized Parameters ========== 
    if ("all" in args.step or "apply_params" in args.step or "optimize_only" in args.step):
        if args.skip_hyperopt:
            logger.info("Skipping Apply Optimized Parameters step as hyperparameter optimization was skipped.")
        else:
            all_models_found, _, _ = check_hyperopt_results_exist()
            if all_models_found:
                logger.info("--- STEP 4: Apply Optimized Parameters ---")
                logger.info("Applying optimized parameters for all 4 classifier models.")
                step_start_time = time.time()
                try:
                    import apply_optimized_params
                    success = apply_optimized_params.main()
                    if success:
                        logger.info(f"✓ Optimized Parameters Applied in {time.time() - step_start_time:.2f} seconds.")
                    else:
                        logger.warning("✗ Failed to apply optimized parameters. Using default config.")
                except Exception as e:
                    logger.error(f"✗ Error applying optimized parameters: {e}", exc_info=True)
                    logger.warning("Continuing with default parameters...")
            else:
                logger.warning("Cannot apply optimized parameters - not all 4 classifier models have optimization results.")
                logger.warning("Please run hyperparameter optimization for all classifier models first.")
                logger.info("Using default parameters for training.")

    # ========== 5. Base Model Training ========== 
    if ("all" in args.step or "train_base" in args.step or "quick" in args.step):
        logger.info("--- STEP 5: Base Model Training ---")
        step_start_time = time.time()
        try:
            train_base.main()
            logger.info(f"✓ Base Model Training completed in {time.time() - step_start_time:.2f} seconds.")
        except Exception as e:
            logger.error(f"✗ Error during Base Model Training: {e}", exc_info=True)
            logger.critical("Pipeline halted due to error in Base Model Training.")
            return

    # ========== 6. Meta-Learner Training ========== 
    if ("all" in args.step or "train_meta" in args.step):
        logger.info("--- STEP 6: Meta-Learner Training ---")
        step_start_time = time.time()
        try:
            train_meta.main(train_val_pred_file_path=args.train_val_pred_file_path)
            logger.info(f"✓ Meta-Learner Training completed in {time.time() - step_start_time:.2f} seconds.")
        except Exception as e:
            logger.error(f"✗ Error during Meta-Learner Training: {e}", exc_info=True)
            logger.critical("Pipeline halted due to error in Meta-Learner Training.")
            return

    # ========== Pipeline Summary ========== 
    total_time = time.time() - start_time_total
    logger.info("="*80)
    logger.info("Pipeline Execution Summary")
    logger.info("="*80)
    logger.info(f"Total execution time: {total_time:.2f} seconds ({total_time/60:.1f} minutes)")
    
    if "optimize_only" in args.step:
        logger.info("✓ Hyperparameter optimization pipeline completed successfully!")
        logger.info("You can now run: python run.py --step train_base --skip_hyperopt")
    elif "quick" in args.step:
        logger.info("✓ Quick training pipeline completed successfully!")
        logger.info("Note: This used default parameters. For better performance, consider running hyperparameter optimization.")
    else:
        logger.info("✓ Full pipeline execution completed successfully!")
    
    logger.info("="*80)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Run the Drug-Disease Repurposing Ensemble Model Pipeline.",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        "--step",
        type=str,
        nargs='+',
        default=["all"],
        choices=["all", "process_split", "prepare_data", "hyperopt", "apply_params", "train_base", "train_meta", "quick", "optimize_only"],
        help="Pipeline step(s) to run. 'all' runs the complete pipeline, 'process_split' processes data splits, 'quick' skips optimization, 'optimize_only' runs only hyperparameter optimization."
    )

    parser.add_argument(
        "--skip_data_prep",
        action="store_true",
        help="Skip data preparation step (assumes data is already prepared)."
    )
    
    parser.add_argument(
        "--skip_process_split",
        action="store_true",
        help="Skip process split data step (assumes processed_data_split.parquet already exists)."
    )
    
    parser.add_argument(
        "--skip_hyperopt",
        action="store_true",
        help="Skip hyperparameter optimization and use default/current config parameters."
    )
    
    parser.add_argument(
        "--train_val_pred_file_path",
        type=str,
        default=None,
        help="Specify the path to the combined training and validation set base model predictions .parquet file for the 'train_meta' step. "
             "If not provided, train_meta.py uses its default path."
    )

    parser.add_argument(
        "--n_calls",
        type=int,
        default=50,
        help="Number of optimization calls per model for hyperparameter optimization (default: 50)"
    )

    cli_args = parser.parse_args()

    # Validate argument combinations
    if "optimize_only" in cli_args.step and len(cli_args.step) > 1:
        logger.error("'optimize_only' cannot be combined with other steps.")
        sys.exit(1)
    
    if "quick" in cli_args.step and len(cli_args.step) > 1:
        logger.error("'quick' cannot be combined with other steps.")
        sys.exit(1)

    main_pipeline(cli_args) 