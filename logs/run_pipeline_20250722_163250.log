2025-07-22 16:32:50,199 - run_pipeline - INFO - Starting pipeline execution with arguments: Namespace(step=['train_meta'], skip_data_prep=False, skip_process_split=False, skip_hyperopt=False, train_val_pred_file_path=None, n_calls=50)
2025-07-22 16:32:50,199 - run_pipeline - INFO - ================================================================================
2025-07-22 16:32:50,199 - run_pipeline - INFO - Drug-Disease Repurposing Ensemble Model Pipeline
2025-07-22 16:32:50,199 - run_pipeline - INFO - ================================================================================
2025-07-22 16:32:50,199 - run_pipeline - INFO - --- STEP 6: Meta-<PERSON>rner Training ---
