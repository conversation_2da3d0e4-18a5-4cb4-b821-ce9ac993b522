2025-07-22 03:38:40,915 - run_pipeline - INFO - Starting pipeline execution with arguments: Namespace(step=['process_split'], skip_data_prep=False, skip_process_split=False, skip_hyperopt=False, train_val_pred_file_path=None, n_calls=50)
2025-07-22 03:38:40,915 - run_pipeline - INFO - ================================================================================
2025-07-22 03:38:40,915 - run_pipeline - INFO - Drug-Disease Repurposing Ensemble Model Pipeline
2025-07-22 03:38:40,915 - run_pipeline - INFO - ================================================================================
2025-07-22 03:38:40,915 - run_pipeline - INFO - --- STEP 1: Process Split Data ---
2025-07-22 03:38:45,192 - run_pipeline - INFO - ✓ Process Split Data completed in 4.28 seconds.
2025-07-22 03:38:45,192 - run_pipeline - INFO - ================================================================================
2025-07-22 03:38:45,192 - run_pipeline - INFO - Pipeline Execution Summary
2025-07-22 03:38:45,192 - run_pipeline - INFO - ================================================================================
2025-07-22 03:38:45,192 - run_pipeline - INFO - Total execution time: 4.28 seconds (0.1 minutes)
2025-07-22 03:38:45,192 - run_pipeline - INFO - ✓ Full pipeline execution completed successfully!
2025-07-22 03:38:45,192 - run_pipeline - INFO - ================================================================================
