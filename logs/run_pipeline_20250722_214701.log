2025-07-22 21:47:01,333 - run_pipeline - INFO - Starting pipeline execution with arguments: Namespace(step=['train_base'], skip_data_prep=False, skip_process_split=False, skip_hyperopt=False, train_val_pred_file_path=None, n_calls=50)
2025-07-22 21:47:01,334 - run_pipeline - INFO - ================================================================================
2025-07-22 21:47:01,334 - run_pipeline - INFO - Drug-Disease Repurposing Ensemble Model Pipeline
2025-07-22 21:47:01,334 - run_pipeline - INFO - ================================================================================
2025-07-22 21:47:01,334 - run_pipeline - INFO - --- STEP 5: Base Model Training ---
2025-07-22 23:13:20,466 - run_pipeline - INFO - ✓ Base Model Training completed in 5179.13 seconds.
2025-07-22 23:13:20,466 - run_pipeline - INFO - ================================================================================
2025-07-22 23:13:20,466 - run_pipeline - INFO - Pipeline Execution Summary
2025-07-22 23:13:20,466 - run_pipeline - INFO - ================================================================================
2025-07-22 23:13:20,466 - run_pipeline - INFO - Total execution time: 5179.13 seconds (86.3 minutes)
2025-07-22 23:13:20,466 - run_pipeline - INFO - ✓ Full pipeline execution completed successfully!
2025-07-22 23:13:20,466 - run_pipeline - INFO - ================================================================================
