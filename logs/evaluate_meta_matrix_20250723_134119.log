2025-07-23 13:41:19,505 - evaluate_meta_matrix - INFO - Loading default stacking model from: /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/analysis/src/../../models/multi_level_stacking_model.joblib
2025-07-23 13:41:52,661 - evaluate_meta_matrix - INFO - Loaded 4166283 node embeddings
2025-07-23 13:41:52,662 - evaluate_meta_matrix - INFO - Matrix predictions file: /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/analysis/src/../../data/raw_data/matrix_predictions.parquet
2025-07-23 13:41:52,662 - evaluate_meta_matrix - INFO - Initializing ensemble predictor with enhanced embeddings...
2025-07-23 13:41:52,663 - evaluate_meta_matrix - INFO - Loading base models...
2025-07-23 13:41:59,738 - evaluate_meta_matrix - INFO - Precomputing enhanced embedding data for batch efficiency...
2025-07-23 13:42:06,968 - evaluate_meta_matrix - INFO - Using batch processing mode for memory efficiency...
2025-07-23 13:42:06,968 - evaluate_meta_matrix - INFO - Processing matrix in batches of 5000000 rows...
2025-07-23 13:42:06,986 - evaluate_meta_matrix - INFO - Total rows to process: 63148207
2025-07-23 13:42:06,989 - evaluate_meta_matrix - INFO - 
Processing batch 1/13 (rows 0-4999999)...
2025-07-23 13:42:07,147 - evaluate_meta_matrix - INFO - Loaded batch: (5000000, 15)
2025-07-23 13:42:07,170 - evaluate_meta_matrix - INFO - Batch 1: Generating ensemble predictions with enhanced embeddings...
