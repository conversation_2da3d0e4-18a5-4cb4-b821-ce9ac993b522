2025-07-22 12:30:54,893 - train_base - INFO - Starting base model training pipeline using fixed TRAIN/VALID splits ...
2025-07-22 12:30:54,893 - train_base - INFO - Note: This training uses parameters from config.py
2025-07-22 12:30:54,894 - train_base - INFO - If you've run hyperparameter optimization, ensure you've applied the optimized parameters first.
2025-07-22 12:30:54,894 - train_base - INFO - Loading full dataset with features from: /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/data/processed_data/full_dataset_with_features.parquet
2025-07-22 12:31:02,816 - train_base - INFO - Loaded dataset with shape: (311142, 6)
2025-07-22 12:31:02,895 - train_base - INFO - Data split: TRAIN (310439, 6), VALID (247, 6), TEST (456, 6)
2025-07-22 12:31:02,909 - train_base - INFO - ===== Training All Base Models using Fixed TRAIN/VALID Split =====
2025-07-22 12:31:36,511 - train_base - INFO - Preparing drug-disease evaluation data...
2025-07-22 12:31:36,511 - train_base - INFO - Starting training for 4 base models...
2025-07-22 12:31:36,516 - train_base - INFO - Training XGBoost Classifier...
2025-07-22 12:32:10,566 - train_base - INFO - Saved base model: xgb_clf to /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/models/base_models
2025-07-22 12:32:10,566 - train_base - INFO - Training LightGBM Classifier...
2025-07-22 12:33:15,408 - train_base - INFO - Saved base model: lgbm_clf to /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/models/base_models
2025-07-22 12:33:15,409 - train_base - INFO - Training CatBoost Classifier...
2025-07-22 12:33:15,409 - train_base - INFO - CatBoost configured to use CPU for training consistency
2025-07-22 12:36:32,961 - train_base - INFO - Saved base model: catboost_clf to /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/models/base_models
2025-07-22 12:36:32,961 - train_base - INFO - Training TabNet Classifier...
2025-07-22 12:36:34,041 - train_base - INFO - TabNet: Training with sample weights and early stopping
2025-07-22 13:57:53,595 - train_base - INFO - Saved base model: tabnet_clf to /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/models/base_models
2025-07-22 13:57:53,595 - train_base - INFO - ===== Finished Training All Base Models =====
2025-07-22 13:57:53,701 - train_base - INFO - Combined predictions shape: (310686, 8) (Train: 310439, Valid: 247)
2025-07-22 13:57:53,799 - train_base - INFO - Training and validation set base model predictions saved to: /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/data/train_val_set_predictions/train_val_set_base_predictions.parquet (Shape: (310686, 8))
2025-07-22 13:57:53,800 - train_base - INFO - Test set data (shape: (456, 6)) is loaded and available for a final evaluation step later.
2025-07-22 13:57:53,800 - train_base - INFO - Base model training pipeline completed.
