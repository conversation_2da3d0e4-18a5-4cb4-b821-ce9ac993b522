2025-07-23 15:46:11,018 - evaluate_meta_matrix - INFO - Loading default stacking model from: /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/analysis/src/../../models/multi_level_stacking_model.joblib
2025-07-23 15:46:45,473 - evaluate_meta_matrix - INFO - Loaded 4166283 node embeddings
2025-07-23 15:46:45,474 - evaluate_meta_matrix - INFO - Matrix predictions file: /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/analysis/src/../../data/raw_data/matrix_predictions.parquet
2025-07-23 15:46:45,474 - evaluate_meta_matrix - INFO - Initializing ensemble predictor with enhanced embeddings...
2025-07-23 15:46:45,475 - evaluate_meta_matrix - INFO - Loading base models...
2025-07-23 15:46:53,055 - evaluate_meta_matrix - INFO - Precomputing enhanced embedding data for batch efficiency...
2025-07-23 15:47:00,195 - evaluate_meta_matrix - INFO - Using batch processing mode for memory efficiency...
2025-07-23 15:47:00,195 - evaluate_meta_matrix - INFO - Processing matrix in batches of 5000000 rows...
2025-07-23 15:47:00,208 - evaluate_meta_matrix - INFO - Total rows to process: 63148207
2025-07-23 15:47:00,212 - evaluate_meta_matrix - INFO - 
Processing batch 1/13 (rows 0-4999999)...
2025-07-23 15:47:00,344 - evaluate_meta_matrix - INFO - Loaded batch: (5000000, 15)
2025-07-23 15:47:00,369 - evaluate_meta_matrix - INFO - Batch 1: Generating ensemble predictions with enhanced embeddings...
