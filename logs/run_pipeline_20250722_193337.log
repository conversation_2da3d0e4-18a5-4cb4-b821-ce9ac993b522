2025-07-22 19:33:37,330 - run_pipeline - INFO - Starting pipeline execution with arguments: Namespace(step=['train_base'], skip_data_prep=False, skip_process_split=False, skip_hyperopt=False, train_val_pred_file_path=None, n_calls=50)
2025-07-22 19:33:37,331 - run_pipeline - INFO - ================================================================================
2025-07-22 19:33:37,331 - run_pipeline - INFO - Drug-Disease Repurposing Ensemble Model Pipeline
2025-07-22 19:33:37,331 - run_pipeline - INFO - ================================================================================
2025-07-22 19:33:37,331 - run_pipeline - INFO - --- STEP 5: Base Model Training ---
2025-07-22 20:59:52,616 - run_pipeline - INFO - ✓ Base Model Training completed in 5175.29 seconds.
2025-07-22 20:59:52,617 - run_pipeline - INFO - ================================================================================
2025-07-22 20:59:52,617 - run_pipeline - INFO - Pipeline Execution Summary
2025-07-22 20:59:52,617 - run_pipeline - INFO - ================================================================================
2025-07-22 20:59:52,617 - run_pipeline - INFO - Total execution time: 5175.29 seconds (86.3 minutes)
2025-07-22 20:59:52,617 - run_pipeline - INFO - ✓ Full pipeline execution completed successfully!
2025-07-22 20:59:52,617 - run_pipeline - INFO - ================================================================================
