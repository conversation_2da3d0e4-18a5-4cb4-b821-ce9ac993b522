2025-07-22 23:34:45,670 - run_pipeline - INFO - Starting pipeline execution with arguments: Namespace(step=['train_meta'], skip_data_prep=False, skip_process_split=False, skip_hyperopt=False, train_val_pred_file_path=None, n_calls=50)
2025-07-22 23:34:45,670 - run_pipeline - INFO - ================================================================================
2025-07-22 23:34:45,670 - run_pipeline - INFO - Drug-Disease Repurposing Ensemble Model Pipeline
2025-07-22 23:34:45,670 - run_pipeline - INFO - ================================================================================
2025-07-22 23:34:45,670 - run_pipeline - INFO - --- STEP 6: Meta-<PERSON>rner Training ---
2025-07-23 00:19:29,600 - run_pipeline - INFO - ✓ Meta-Learner Training completed in 2683.93 seconds.
2025-07-23 00:19:29,600 - run_pipeline - INFO - ================================================================================
2025-07-23 00:19:29,600 - run_pipeline - INFO - Pipeline Execution Summary
2025-07-23 00:19:29,601 - run_pipeline - INFO - ================================================================================
2025-07-23 00:19:29,601 - run_pipeline - INFO - Total execution time: 2683.93 seconds (44.7 minutes)
2025-07-23 00:19:29,601 - run_pipeline - INFO - ✓ Full pipeline execution completed successfully!
2025-07-23 00:19:29,601 - run_pipeline - INFO - ================================================================================
