2025-07-23 12:46:26,636 - run_pipeline - INFO - Starting pipeline execution with arguments: Namespace(step=['train_meta'], skip_data_prep=False, skip_process_split=False, skip_hyperopt=False, train_val_pred_file_path=None, n_calls=50)
2025-07-23 12:46:26,638 - run_pipeline - INFO - ================================================================================
2025-07-23 12:46:26,638 - run_pipeline - INFO - Drug-Disease Repurposing Ensemble Model Pipeline
2025-07-23 12:46:26,638 - run_pipeline - INFO - ================================================================================
2025-07-23 12:46:26,638 - run_pipeline - INFO - --- STEP 6: Meta-<PERSON>rner Training ---
2025-07-23 13:31:33,909 - run_pipeline - INFO - ✓ Meta-Learner Training completed in 2707.27 seconds.
2025-07-23 13:31:33,910 - run_pipeline - INFO - ================================================================================
2025-07-23 13:31:33,910 - run_pipeline - INFO - Pipeline Execution Summary
2025-07-23 13:31:33,910 - run_pipeline - INFO - ================================================================================
2025-07-23 13:31:33,910 - run_pipeline - INFO - Total execution time: 2707.27 seconds (45.1 minutes)
2025-07-23 13:31:33,910 - run_pipeline - INFO - ✓ Full pipeline execution completed successfully!
2025-07-23 13:31:33,910 - run_pipeline - INFO - ================================================================================
