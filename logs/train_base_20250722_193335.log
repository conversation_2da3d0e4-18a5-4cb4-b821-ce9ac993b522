2025-07-22 19:33:37,331 - train_base - INFO - Starting base model training pipeline using fixed TRAIN/VALID splits ...
2025-07-22 19:33:37,331 - train_base - INFO - Note: This training uses parameters from config.py
2025-07-22 19:33:37,331 - train_base - INFO - If you've run hyperparameter optimization, ensure you've applied the optimized parameters first.
2025-07-22 19:33:37,331 - train_base - INFO - Loading full dataset with features from: /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/data/processed_data/full_dataset_with_features.parquet
2025-07-22 19:33:44,542 - train_base - INFO - Loaded dataset with shape: (311142, 6)
2025-07-22 19:33:44,624 - train_base - INFO - Data split: TRAIN (310439, 6), VALID (247, 6), TEST (456, 6)
2025-07-22 19:33:44,637 - train_base - INFO - ===== Training All Base Models using Fixed TRAIN/VALID Split =====
2025-07-22 19:34:17,885 - train_base - INFO - Training data for meta-learning: 310439 pairs (weight != 0.2) out of 310439 total training pairs
2025-07-22 19:34:17,886 - train_base - INFO - Validation data for meta-learning: 247 pairs (all validation pairs)
2025-07-22 19:34:17,947 - train_base - INFO - Preparing drug-disease evaluation data...
2025-07-22 19:34:17,947 - train_base - INFO - Starting training for 4 base models...
2025-07-22 19:34:17,956 - train_base - INFO - Training XGBoost Classifier...
2025-07-22 19:34:49,850 - train_base - INFO - Saved base model: xgb_clf to /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/models/base_models
2025-07-22 19:34:49,855 - train_base - INFO - Training LightGBM Classifier...
2025-07-22 19:35:44,122 - train_base - INFO - Saved base model: lgbm_clf to /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/models/base_models
2025-07-22 19:35:44,127 - train_base - INFO - Training CatBoost Classifier...
2025-07-22 19:35:44,127 - train_base - INFO - CatBoost configured to use CPU for training consistency
2025-07-22 19:38:57,870 - train_base - INFO - Saved base model: catboost_clf to /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/models/base_models
2025-07-22 19:38:57,874 - train_base - INFO - Training TabNet Classifier...
2025-07-22 19:38:58,822 - train_base - INFO - TabNet: Training with sample weights and early stopping
2025-07-22 20:59:52,363 - train_base - INFO - Saved base model: tabnet_clf to /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/models/base_models
2025-07-22 20:59:52,370 - train_base - INFO - ===== Finished Training All Base Models =====
2025-07-22 20:59:52,500 - train_base - INFO - Combined predictions shape: (310686, 8) (Train filtered: 310439, Valid: 247)
2025-07-22 20:59:52,501 - train_base - INFO - Note: Training predictions filtered to weight == 1.0 pairs only for meta-learning
2025-07-22 20:59:52,612 - train_base - INFO - Training and validation set base model predictions saved to: /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/data/train_val_set_predictions/train_val_set_base_predictions.parquet (Shape: (310686, 8))
2025-07-22 20:59:52,612 - train_base - INFO - Test set data (shape: (456, 6)) is loaded and available for a final evaluation step later.
2025-07-22 20:59:52,612 - train_base - INFO - Base model training pipeline completed.
