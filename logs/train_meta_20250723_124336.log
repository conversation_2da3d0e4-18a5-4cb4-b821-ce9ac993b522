2025-07-23 12:43:36,038 - train_meta - INFO - Starting Multi-Level Stacking meta-learner training pipeline...
2025-07-23 12:43:36,038 - train_meta - INFO - Loading combined training and validation set base model predictions from: /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/data/train_val_set_predictions/train_val_set_base_predictions.parquet
2025-07-23 12:43:36,149 - train_meta - INFO - Loaded Polars combined predictions with shape: (34139, 8)
2025-07-23 12:43:36,202 - train_meta - INFO - Split distribution: {'split': ['TRAIN', 'VALID'], 'count': [33892, 247]}
2025-07-23 12:43:36,203 - train_meta - INFO - Preparing combined training and validation data for multi-level stacking...
2025-07-23 12:43:36,236 - train_meta - INFO - Cleaned data split distribution: {'split': ['TRAIN', 'VALID'], 'count': [33892, 247]}
2025-07-23 12:43:36,237 - train_meta - INFO - Prepared 34139 total samples for meta-learning
2025-07-23 12:43:36,237 - train_meta - INFO - Training Multi-Level Stacking ensemble on combined training and validation data...
2025-07-23 12:43:36,239 - train_meta - INFO - Level 2: Training diverse meta-learners...
2025-07-23 12:43:36,247 - train_meta - INFO - Base model probabilities will be passed directly to Level 3
2025-07-23 12:43:36,247 - train_meta - INFO - Training Gaussian Process meta-learner with enhanced features...
