2025-07-22 21:47:01,334 - train_base - INFO - Starting base model training pipeline using fixed TRAIN/VALID splits ...
2025-07-22 21:47:01,334 - train_base - INFO - Note: This training uses parameters from config.py
2025-07-22 21:47:01,334 - train_base - INFO - If you've run hyperparameter optimization, ensure you've applied the optimized parameters first.
2025-07-22 21:47:01,334 - train_base - INFO - Loading full dataset with features from: /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/data/processed_data/full_dataset_with_features.parquet
2025-07-22 21:47:08,607 - train_base - INFO - Loaded dataset with shape: (311142, 6)
2025-07-22 21:47:08,679 - train_base - INFO - Data split: TRAIN (310439, 6), VALID (247, 6), TEST (456, 6)
2025-07-22 21:47:08,689 - train_base - INFO - ===== Training All Base Models using Fixed TRAIN/VALID Split =====
2025-07-22 21:47:53,845 - train_base - INFO - Training data for meta-learning: 33892 pairs (weight == 1.0) out of 310439 total training pairs
2025-07-22 21:47:53,846 - train_base - INFO - Validation data for meta-learning: 247 pairs (all validation pairs)
2025-07-22 21:47:53,853 - train_base - INFO - Preparing drug-disease evaluation data...
2025-07-22 21:47:53,853 - train_base - INFO - Starting training for 4 base models...
2025-07-22 21:47:53,858 - train_base - INFO - Training XGBoost Classifier...
2025-07-22 21:48:26,933 - train_base - INFO - Saved base model: xgb_clf to /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/models/base_models
2025-07-22 21:48:26,936 - train_base - INFO - Training LightGBM Classifier...
2025-07-22 21:49:21,636 - train_base - INFO - Saved base model: lgbm_clf to /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/models/base_models
2025-07-22 21:49:21,640 - train_base - INFO - Training CatBoost Classifier...
2025-07-22 21:49:21,640 - train_base - INFO - CatBoost configured to use CPU for training consistency
2025-07-22 21:52:34,780 - train_base - INFO - Saved base model: catboost_clf to /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/models/base_models
2025-07-22 21:52:34,783 - train_base - INFO - Training TabNet Classifier...
2025-07-22 21:52:35,938 - train_base - INFO - TabNet: Training with sample weights and early stopping
2025-07-22 23:13:20,387 - train_base - INFO - Saved base model: tabnet_clf to /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/models/base_models
2025-07-22 23:13:20,404 - train_base - INFO - ===== Finished Training All Base Models =====
2025-07-22 23:13:20,423 - train_base - INFO - Combined predictions shape: (34139, 8) (Train filtered: 33892, Valid: 247)
2025-07-22 23:13:20,423 - train_base - INFO - Note: Training predictions filtered to weight == 1.0 pairs only for meta-learning
2025-07-22 23:13:20,461 - train_base - INFO - Training and validation set base model predictions saved to: /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/data/train_val_set_predictions/train_val_set_base_predictions.parquet (Shape: (34139, 8))
2025-07-22 23:13:20,462 - train_base - INFO - Test set data (shape: (456, 6)) is loaded and available for a final evaluation step later.
2025-07-22 23:13:20,462 - train_base - INFO - Base model training pipeline completed.
