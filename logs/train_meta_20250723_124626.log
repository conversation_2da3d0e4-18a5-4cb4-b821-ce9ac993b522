2025-07-23 12:46:26,638 - train_meta - INFO - Starting Multi-Level Stacking meta-learner training pipeline...
2025-07-23 12:46:26,638 - train_meta - INFO - Loading combined training and validation set base model predictions from: /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/data/train_val_set_predictions/train_val_set_base_predictions.parquet
2025-07-23 12:46:26,721 - train_meta - INFO - Loaded Polars combined predictions with shape: (34139, 8)
2025-07-23 12:46:26,784 - train_meta - INFO - Split distribution: {'split': ['TRAIN', 'VALID'], 'count': [33892, 247]}
2025-07-23 12:46:26,785 - train_meta - INFO - Preparing combined training and validation data for multi-level stacking...
2025-07-23 12:46:26,817 - train_meta - INFO - Cleaned data split distribution: {'split': ['VALID', 'TRAIN'], 'count': [247, 33892]}
2025-07-23 12:46:26,818 - train_meta - INFO - Prepared 34139 total samples for meta-learning
2025-07-23 12:46:26,819 - train_meta - INFO - Training Multi-Level Stacking ensemble on combined training and validation data...
2025-07-23 12:46:26,820 - train_meta - INFO - Level 2: Training diverse meta-learners...
2025-07-23 12:46:26,828 - train_meta - INFO - Base model probabilities will be passed directly to Level 3
2025-07-23 12:46:26,828 - train_meta - INFO - Training Gaussian Process meta-learner with enhanced features...
2025-07-23 12:46:26,864 - train_meta - INFO - Using RBF kernel
2025-07-23 13:31:19,858 - train_meta - INFO - Gaussian Process meta-learner trained. Score range: [0.001, 0.999]
2025-07-23 13:31:19,859 - train_meta - INFO - Training MLP meta-learner...
2025-07-23 13:31:21,279 - train_meta - INFO - MLP meta-learner trained. Score range: [0.000, 1.000]
2025-07-23 13:31:21,279 - train_meta - INFO - Computing confidence-based predictions...
2025-07-23 13:31:21,306 - train_meta - INFO - Confidence-based predictions computed. Score range: [0.001, 0.991]
2025-07-23 13:31:21,306 - train_meta - INFO - Level 3: Training final meta-learner...
2025-07-23 13:31:21,307 - train_meta - INFO - Final meta-learner input shape: (34139, 7) (4 base + 3 meta predictions)
2025-07-23 13:31:21,351 - train_meta - INFO - Multi-Level Stacking training completed!
2025-07-23 13:31:21,351 - train_meta - INFO - Final meta-learner coefficients: [ 1.28774974  0.9244785  -0.28589785  1.96350795  3.28463824  3.08481383
  0.6749671 ]
2025-07-23 13:31:21,351 - train_meta - INFO - Final meta-learner intercept: -5.407122673091949
2025-07-23 13:31:33,884 - train_meta - INFO - Multi-Level Stacking model saved to: /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/models/multi_level_stacking_model.joblib
