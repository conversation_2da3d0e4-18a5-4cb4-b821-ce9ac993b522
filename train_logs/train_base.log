2025-07-22 21:47:01,333 - run_pipeline - INFO - Starting pipeline execution with arguments: Namespace(step=['train_base'], skip_data_prep=False, skip_process_split=False, skip_hyperopt=False, train_val_pred_file_path=None, n_calls=50)
2025-07-22 21:47:01,334 - run_pipeline - INFO - ================================================================================
2025-07-22 21:47:01,334 - run_pipeline - INFO - Drug-Disease Repurposing Ensemble Model Pipeline
2025-07-22 21:47:01,334 - run_pipeline - INFO - ================================================================================
2025-07-22 21:47:01,334 - run_pipeline - INFO - --- STEP 5: Base Model Training ---
2025-07-22 21:47:01,334 - train_base - INFO - Starting base model training pipeline using fixed TRAIN/VALID splits ...
2025-07-22 21:47:01,334 - train_base - INFO - Note: This training uses parameters from config.py
2025-07-22 21:47:01,334 - train_base - INFO - If you've run hyperparameter optimization, ensure you've applied the optimized parameters first.
2025-07-22 21:47:01,334 - train_base - INFO - Loading full dataset with features from: /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/data/processed_data/full_dataset_with_features.parquet
2025-07-22 21:47:08,607 - train_base - INFO - Loaded dataset with shape: (311142, 6)
2025-07-22 21:47:08,679 - train_base - INFO - Data split: TRAIN (310439, 6), VALID (247, 6), TEST (456, 6)
2025-07-22 21:47:08,689 - train_base - INFO - ===== Training All Base Models using Fixed TRAIN/VALID Split =====
2025-07-22 21:47:53,845 - train_base - INFO - Training data for meta-learning: 33892 pairs (weight == 1.0) out of 310439 total training pairs
2025-07-22 21:47:53,846 - train_base - INFO - Validation data for meta-learning: 247 pairs (all validation pairs)
2025-07-22 21:47:53,853 - train_base - INFO - Preparing drug-disease evaluation data...
2025-07-22 21:47:53,853 - train_base - INFO - Starting training for 4 base models...

Training base models:   0%|          | 0/4 [00:00<?, ?it/s]2025-07-22 21:47:53,858 - train_base - INFO - Training XGBoost Classifier...
/home/<USER>/cqm5886/miniconda3/envs/exps2_ensemble_model_new_env/lib/python3.11/site-packages/xgboost/core.py:729: UserWarning: [21:48:24] WARNING: /workspace/src/common/error_msg.cc:58: Falling back to prediction using DMatrix due to mismatched devices. This might lead to higher memory usage and slower performance. XGBoost is running on: cuda:3, while the input data is on: cpu.
Potential solutions:
- Use a data structure that matches the device ordinal in the booster.
- Set the device for booster before call to inplace_predict.

This warning will only be shown once.

  return func(**kwargs)
2025-07-22 21:48:26,933 - train_base - INFO - Saved base model: xgb_clf to /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/models/base_models

Training base models:  25%|██▌       | 1/4 [00:33<01:39, 33.08s/it]2025-07-22 21:48:26,936 - train_base - INFO - Training LightGBM Classifier...
/home/<USER>/cqm5886/miniconda3/envs/exps2_ensemble_model_new_env/lib/python3.11/site-packages/sklearn/utils/validation.py:2749: UserWarning: X does not have valid feature names, but LGBMClassifier was fitted with feature names
  warnings.warn(
/home/<USER>/cqm5886/miniconda3/envs/exps2_ensemble_model_new_env/lib/python3.11/site-packages/sklearn/utils/validation.py:2749: UserWarning: X does not have valid feature names, but LGBMClassifier was fitted with feature names
  warnings.warn(
2025-07-22 21:49:21,636 - train_base - INFO - Saved base model: lgbm_clf to /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/models/base_models

Training base models:  50%|█████     | 2/4 [01:27<01:31, 45.80s/it]2025-07-22 21:49:21,640 - train_base - INFO - Training CatBoost Classifier...
2025-07-22 21:49:21,640 - train_base - INFO - CatBoost configured to use CPU for training consistency
2025-07-22 21:52:34,780 - train_base - INFO - Saved base model: catboost_clf to /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/models/base_models

Training base models:  75%|███████▌  | 3/4 [04:40<01:53, 113.08s/it]2025-07-22 21:52:34,783 - train_base - INFO - Training TabNet Classifier...
/home/<USER>/cqm5886/miniconda3/envs/exps2_ensemble_model_new_env/lib/python3.11/site-packages/pytorch_tabnet/abstract_model.py:82: UserWarning: Device used : cuda:2
  warnings.warn(f"Device used : {self.device}")
2025-07-22 21:52:35,938 - train_base - INFO - TabNet: Training with sample weights and early stopping
/home/<USER>/cqm5886/miniconda3/envs/exps2_ensemble_model_new_env/lib/python3.11/site-packages/pytorch_tabnet/callbacks.py:172: UserWarning: Best weights from best epoch are automatically used!
  warnings.warn(wrn_msg)
2025-07-22 23:13:20,387 - train_base - INFO - Saved base model: tabnet_clf to /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/models/base_models

Training base models: 100%|██████████| 4/4 [1:25:26<00:00, 1981.43s/it]
Training base models: 100%|██████████| 4/4 [1:25:26<00:00, 1281.64s/it]
2025-07-22 23:13:20,404 - train_base - INFO - ===== Finished Training All Base Models =====
2025-07-22 23:13:20,423 - train_base - INFO - Combined predictions shape: (34139, 8) (Train filtered: 33892, Valid: 247)
2025-07-22 23:13:20,423 - train_base - INFO - Note: Training predictions filtered to weight == 1.0 pairs only for meta-learning
2025-07-22 23:13:20,461 - train_base - INFO - Training and validation set base model predictions saved to: /scratch/cqm5886/MATRIX_project/proposed_exps/exps2/Ensemble_models_v3_disease_split_temp/data/train_val_set_predictions/train_val_set_base_predictions.parquet (Shape: (34139, 8))
2025-07-22 23:13:20,462 - train_base - INFO - Test set data (shape: (456, 6)) is loaded and available for a final evaluation step later.
2025-07-22 23:13:20,462 - train_base - INFO - Base model training pipeline completed.
2025-07-22 23:13:20,466 - run_pipeline - INFO - ✓ Base Model Training completed in 5179.13 seconds.
2025-07-22 23:13:20,466 - run_pipeline - INFO - ================================================================================
2025-07-22 23:13:20,466 - run_pipeline - INFO - Pipeline Execution Summary
2025-07-22 23:13:20,466 - run_pipeline - INFO - ================================================================================
2025-07-22 23:13:20,466 - run_pipeline - INFO - Total execution time: 5179.13 seconds (86.3 minutes)
2025-07-22 23:13:20,466 - run_pipeline - INFO - ✓ Full pipeline execution completed successfully!
2025-07-22 23:13:20,466 - run_pipeline - INFO - ================================================================================
Training until validation scores don't improve for 50 rounds
Early stopping, best iteration is:
[273]	valid_0's binary_logloss: 0.549532
epoch 0  | loss: 0.41885 | val_0_auc: 0.75797 |  0:01:52s
epoch 1  | loss: 0.23523 | val_0_auc: 0.91998 |  0:03:46s
epoch 2  | loss: 0.1309  | val_0_auc: 0.88226 |  0:05:41s
epoch 3  | loss: 0.0834  | val_0_auc: 0.95343 |  0:07:35s
epoch 4  | loss: 0.06126 | val_0_auc: 0.90243 |  0:09:29s
epoch 5  | loss: 0.05034 | val_0_auc: 0.8718  |  0:11:22s
epoch 6  | loss: 0.04175 | val_0_auc: 0.87741 |  0:13:18s
epoch 7  | loss: 0.03484 | val_0_auc: 0.88895 |  0:15:13s
epoch 8  | loss: 0.02971 | val_0_auc: 0.90863 |  0:17:07s
epoch 9  | loss: 0.02704 | val_0_auc: 0.9127  |  0:19:02s
epoch 10 | loss: 0.02703 | val_0_auc: 0.86705 |  0:20:57s
epoch 11 | loss: 0.0221  | val_0_auc: 0.9186  |  0:22:53s
epoch 12 | loss: 0.02067 | val_0_auc: 0.92214 |  0:24:49s
epoch 13 | loss: 0.01951 | val_0_auc: 0.90375 |  0:26:46s
epoch 14 | loss: 0.01803 | val_0_auc: 0.87689 |  0:28:41s
epoch 15 | loss: 0.01521 | val_0_auc: 0.85249 |  0:30:35s
epoch 16 | loss: 0.01639 | val_0_auc: 0.88627 |  0:32:30s
epoch 17 | loss: 0.01481 | val_0_auc: 0.89237 |  0:34:23s
epoch 18 | loss: 0.01372 | val_0_auc: 0.89571 |  0:36:20s
epoch 19 | loss: 0.01252 | val_0_auc: 0.84488 |  0:38:19s
epoch 20 | loss: 0.01265 | val_0_auc: 0.84101 |  0:40:17s
epoch 21 | loss: 0.01092 | val_0_auc: 0.77155 |  0:42:14s
epoch 22 | loss: 0.01177 | val_0_auc: 0.82054 |  0:44:12s
epoch 23 | loss: 0.01189 | val_0_auc: 0.86495 |  0:46:10s
epoch 24 | loss: 0.01208 | val_0_auc: 0.82022 |  0:48:07s
epoch 25 | loss: 0.00958 | val_0_auc: 0.83117 |  0:50:04s
epoch 26 | loss: 0.01191 | val_0_auc: 0.83478 |  0:52:03s
epoch 27 | loss: 0.00884 | val_0_auc: 0.85977 |  0:54:03s
epoch 28 | loss: 0.00886 | val_0_auc: 0.83747 |  0:56:00s
epoch 29 | loss: 0.00883 | val_0_auc: 0.81451 |  0:57:58s
epoch 30 | loss: 0.00869 | val_0_auc: 0.79293 |  0:59:57s
epoch 31 | loss: 0.00832 | val_0_auc: 0.76558 |  1:01:55s

Early stopping occurred at epoch 31 with best_epoch = 3 and best_val_0_auc = 0.95343
